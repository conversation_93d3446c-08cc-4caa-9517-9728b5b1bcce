<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快照按钮测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold mb-6">快照按钮测试页面</h1>
        
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">测试控制</h2>
            <div class="flex gap-4">
                <button onclick="loadTestSnapshots()" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                    加载测试快照
                </button>
                <button onclick="checkButtons()" class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                    检查按钮
                </button>
                <button onclick="clearTest()" class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600">
                    清空测试
                </button>
            </div>
        </div>

        <div id="testResults" class="bg-white rounded-lg shadow p-6 mb-6">
            <h3 class="text-lg font-semibold mb-4">测试结果</h3>
            <div id="results"></div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold mb-4">快照卡片测试</h3>
            <div id="snapshotGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- 快照卡片将在这里显示 -->
            </div>
        </div>
    </div>

    <script>
        // 测试数据
        const testSnapshots = [
            {
                id: 1,
                title: "测试快照 1",
                url: "https://example.com",
                description: "这是第一个测试快照",
                category: "测试",
                status: "默认",
                snapshot_url: "https://s0.wp.com/mshots/v1/https://example.com?w=1200&h=800"
            },
            {
                id: 2,
                title: "测试快照 2",
                url: "https://google.com",
                description: "这是第二个测试快照",
                category: "搜索",
                status: "收藏",
                snapshot_url: "https://s0.wp.com/mshots/v1/https://google.com?w=1200&h=800"
            },
            {
                id: 3,
                title: "测试快照 3",
                url: "https://github.com",
                description: "这是第三个测试快照",
                category: "开发",
                status: "已读",
                snapshot_url: ""
            }
        ];

        // 创建快照卡片
        function createSnapshotCard(snapshot) {
            const hasSnapshotUrl = snapshot.snapshot_url && snapshot.snapshot_url.trim();
            const snapshotPreview = hasSnapshotUrl 
                ? `<img src="${snapshot.snapshot_url}" alt="${snapshot.title}" class="w-full h-48 object-cover bg-gray-200" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuaXoOW/q+eFpzwvdGV4dD48L3N2Zz4='">`
                : `<div class="w-full h-48 bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-4xl">📸</div>`;

            const iconHtml = `<div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center text-blue-600 font-bold">${snapshot.title.charAt(0).toUpperCase()}</div>`;

            return `
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
                    <!-- 快照预览 -->
                    <div class="relative">
                        ${snapshotPreview}
                        <div class="absolute top-2 right-2">
                            <span class="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                ${snapshot.status || '默认'}
                            </span>
                        </div>
                    </div>

                    <!-- 操作按钮区域 -->
                    <div class="p-4 border-b border-gray-100">
                        <div class="flex items-center justify-between gap-2">
                            <div class="flex items-center gap-2">
                                <a href="${snapshot.url}" target="_blank" class="px-3 py-1.5 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
                                    访问
                                </a>
                                ${hasSnapshotUrl ? `<a href="${snapshot.snapshot_url}" target="_blank" class="px-3 py-1.5 text-xs bg-green-500 text-white rounded hover:bg-green-600 transition-colors">快照</a>` : ''}
                            </div>
                            <div class="flex items-center gap-1">
                                <button onclick="editSnapshot(${snapshot.id})" class="px-3 py-1.5 text-xs bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors" data-snapshot-id="${snapshot.id}">
                                    编辑
                                </button>
                                <button onclick="deleteSnapshot(${snapshot.id})" class="px-3 py-1.5 text-xs bg-red-500 text-white rounded hover:bg-red-600 transition-colors" data-snapshot-id="${snapshot.id}">
                                    删除
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 卡片内容 -->
                    <div class="p-4">
                        <!-- 标题和图标 -->
                        <div class="flex items-start gap-3 mb-3">
                            ${iconHtml}
                            <div class="flex-1 min-w-0">
                                <h3 class="text-sm font-medium text-gray-900 truncate" title="${snapshot.title}">
                                    ${snapshot.title}
                                </h3>
                                <p class="text-xs text-gray-500 truncate" title="${snapshot.url}">
                                    ${snapshot.url}
                                </p>
                            </div>
                        </div>

                        <!-- 描述 -->
                        ${snapshot.description ? `
                            <div class="mb-3">
                                <p class="text-sm text-gray-600 line-clamp-2">${snapshot.description}</p>
                            </div>
                        ` : ''}

                        <!-- 分类标签 -->
                        <div class="flex justify-center">
                            <span class="px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700">
                                ${snapshot.category || '默认'}
                            </span>
                        </div>
                    </div>
                </div>
            `;
        }

        // 加载测试快照
        function loadTestSnapshots() {
            const grid = document.getElementById('snapshotGrid');
            grid.innerHTML = testSnapshots.map(snapshot => createSnapshotCard(snapshot)).join('');
            
            const results = document.getElementById('results');
            results.innerHTML = `
                <div class="p-3 bg-green-100 text-green-800 rounded">
                    ✅ 已加载 ${testSnapshots.length} 个测试快照
                </div>
            `;
        }

        // 检查按钮
        function checkButtons() {
            const editButtons = document.querySelectorAll('[data-snapshot-id]');
            const snapshotCards = document.querySelectorAll('#snapshotGrid > div');
            
            const results = document.getElementById('results');
            results.innerHTML = `
                <div class="space-y-3">
                    <div class="p-3 bg-blue-100 text-blue-800 rounded">
                        📊 快照卡片数量: ${snapshotCards.length}
                    </div>
                    <div class="p-3 bg-purple-100 text-purple-800 rounded">
                        🔘 操作按钮数量: ${editButtons.length}
                    </div>
                    <div class="p-3 ${editButtons.length > 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'} rounded">
                        ${editButtons.length > 0 ? '✅ 按钮显示正常' : '❌ 按钮未显示'}
                    </div>
                    <div class="p-3 bg-gray-100 text-gray-800 rounded">
                        <h4 class="font-medium mb-2">按钮详情:</h4>
                        ${Array.from(editButtons).map((btn, index) => 
                            `<div>按钮 ${index + 1}: ID=${btn.getAttribute('data-snapshot-id')}, 文本="${btn.textContent.trim()}"</div>`
                        ).join('')}
                    </div>
                </div>
            `;
            
            console.log('=== 按钮检查结果 ===');
            console.log('快照卡片数量:', snapshotCards.length);
            console.log('操作按钮数量:', editButtons.length);
            console.log('按钮元素:', editButtons);
        }

        // 清空测试
        function clearTest() {
            document.getElementById('snapshotGrid').innerHTML = '';
            document.getElementById('results').innerHTML = `
                <div class="p-3 bg-gray-100 text-gray-600 rounded">
                    🧹 测试已清空
                </div>
            `;
        }

        // 模拟编辑函数
        function editSnapshot(id) {
            alert(`编辑快照 ID: ${id}`);
            console.log('编辑快照:', id);
        }

        // 模拟删除函数
        function deleteSnapshot(id) {
            if (confirm(`确定要删除快照 ID: ${id} 吗？`)) {
                alert(`删除快照 ID: ${id}`);
                console.log('删除快照:', id);
            }
        }
    </script>
</body>
</html>
