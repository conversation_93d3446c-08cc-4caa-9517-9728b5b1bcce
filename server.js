const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const path = require('path');
const db = require('./database');
const snapshotDb = require('./snapshotDatabase');
const autoExport = require('./autoExport');
const SnapshotAutoExport = require('./snapshotAutoExport');
const faviconCache = require('./faviconCache');

const app = express();
const PORT = process.env.PORT || 3004;

// 中间件
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(express.static('public'));

// 初始化数据库
db.initDatabase();

// 初始化快照数据库
snapshotDb.initSnapshotDatabase();

// 初始化图标缓存系统
faviconCache.initFaviconCache();

// 初始化自动导出系统
autoExport.initAutoExport();

// 初始化快照自动导出系统
const snapshotAutoExport = new SnapshotAutoExport();

// API 路由

// 获取所有收藏
app.get('/api/bookmarks', (req, res) => {
    const { category, search, status, page = 1, limit = 20 } = req.query;
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);

    if (search) {
        // 搜索收藏
        db.searchBookmarksPaginated(search, pageNum, limitNum, (err, rows) => {
            if (err) {
                res.status(500).json({ error: err.message });
                return;
            }

            // 获取搜索结果总数
            db.getSearchBookmarksCount(search, (countErr, countResult) => {
                if (countErr) {
                    res.status(500).json({ error: countErr.message });
                    return;
                }

                const total = countResult.total;
                const totalPages = Math.ceil(total / limitNum);

                res.json({
                    data: rows,
                    pagination: {
                        page: pageNum,
                        limit: limitNum,
                        total: total,
                        totalPages: totalPages,
                        hasNext: pageNum < totalPages,
                        hasPrev: pageNum > 1
                    }
                });
            });
        });
    } else if (category) {
        // 按分类筛选收藏
        db.getBookmarksByCategoryPaginated(category, pageNum, limitNum, (err, rows) => {
            if (err) {
                res.status(500).json({ error: err.message });
                return;
            }

            // 获取分类收藏总数
            db.getBookmarksByCategoryCount(category, (countErr, countResult) => {
                if (countErr) {
                    res.status(500).json({ error: countErr.message });
                    return;
                }

                const total = countResult.total;
                const totalPages = Math.ceil(total / limitNum);

                res.json({
                    data: rows,
                    pagination: {
                        page: pageNum,
                        limit: limitNum,
                        total: total,
                        totalPages: totalPages,
                        hasNext: pageNum < totalPages,
                        hasPrev: pageNum > 1
                    }
                });
            });
        });
    } else if (status) {
        // 按状态筛选收藏
        db.getBookmarksByStatusPaginated(status, pageNum, limitNum, (err, rows) => {
            if (err) {
                res.status(500).json({ error: err.message });
                return;
            }

            // 获取状态收藏总数
            db.getBookmarksByStatusCount(status, (countErr, countResult) => {
                if (countErr) {
                    res.status(500).json({ error: countErr.message });
                    return;
                }

                const total = countResult.total;
                const totalPages = Math.ceil(total / limitNum);

                res.json({
                    data: rows,
                    pagination: {
                        page: pageNum,
                        limit: limitNum,
                        total: total,
                        totalPages: totalPages,
                        hasNext: pageNum < totalPages,
                        hasPrev: pageNum > 1
                    }
                });
            });
        });
    } else {
        // 获取所有收藏
        db.getBookmarksPaginated(pageNum, limitNum, (err, rows) => {
            if (err) {
                res.status(500).json({ error: err.message });
                return;
            }

            // 获取总数
            db.getBookmarksCount((countErr, countResult) => {
                if (countErr) {
                    res.status(500).json({ error: countErr.message });
                    return;
                }

                const total = countResult.total;
                const totalPages = Math.ceil(total / limitNum);

                res.json({
                    data: rows,
                    pagination: {
                        page: pageNum,
                        limit: limitNum,
                        total: total,
                        totalPages: totalPages,
                        hasNext: pageNum < totalPages,
                        hasPrev: pageNum > 1
                    }
                });
            });
        });
    }
});

// 添加新收藏
app.post('/api/bookmarks', (req, res) => {
    const bookmark = req.body;
    
    if (!bookmark.title || !bookmark.url) {
        res.status(400).json({ error: '标题和网址是必填项' });
        return;
    }
    
    db.addBookmark(bookmark, (err, id) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json({ id, message: '收藏添加成功' });
    });
});

// 更新收藏
app.put('/api/bookmarks/:id', (req, res) => {
    const id = req.params.id;
    const bookmark = req.body;
    
    if (!bookmark.title || !bookmark.url) {
        res.status(400).json({ error: '标题和网址是必填项' });
        return;
    }
    
    db.updateBookmark(id, bookmark, (err) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json({ message: '收藏更新成功' });
    });
});

// 删除收藏
app.delete('/api/bookmarks/:id', (req, res) => {
    const id = req.params.id;
    
    db.deleteBookmark(id, (err) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json({ message: '收藏删除成功' });
    });
});

// 获取所有分类
app.get('/api/categories', (req, res) => {
    db.getAllCategories((err, rows) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json(rows.map(row => row.category));
    });
});

// 获取所有状态
app.get('/api/statuses', (req, res) => {
    db.getAllStatuses((err, rows) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json(rows.map(row => row.status));
    });
});

// 获取网站图标（已废弃，现在使用前端生成）
app.get('/api/favicon', async (req, res) => {
    res.json({
        message: '图标功能已迁移到前端，使用MDI图标和SVG生成',
        favicon: null
    });
});

// 批量获取网站图标（已废弃，现在使用前端生成）
app.post('/api/favicons', async (req, res) => {
    res.json({
        message: '图标功能已迁移到前端，使用MDI图标和SVG生成'
    });
});

// 手动导出API
app.post('/api/export', async (req, res) => {
    try {
        const result = await autoExport.exportToExcel();
        res.json({
            success: true,
            message: '导出成功',
            count: result.count,
            file: path.basename(result.file),
            timestamp: result.timestamp
        });
    } catch (error) {
        console.error('手动导出失败:', error);
        res.status(500).json({
            success: false,
            error: '导出失败',
            message: error.message
        });
    }
});

// 获取导出文件信息
app.get('/api/export/info', (req, res) => {
    const fs = require('fs');

    try {
        if (fs.existsSync(autoExport.EXPORT_FILE)) {
            const stats = fs.statSync(autoExport.EXPORT_FILE);
            res.json({
                exists: true,
                file: path.basename(autoExport.EXPORT_FILE),
                size: stats.size,
                lastModified: stats.mtime.toLocaleString('zh-CN'),
                path: autoExport.EXPORT_DIR
            });
        } else {
            res.json({
                exists: false,
                message: '导出文件不存在'
            });
        }
    } catch (error) {
        res.status(500).json({
            error: '获取文件信息失败',
            message: error.message
        });
    }
});

// 快照页面路由
app.get('/kz', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'snapshot.html'));
});

// 快照API路由

// 获取所有快照收藏
app.get('/kz/api/snapshots', (req, res) => {
    snapshotDb.snapshotBookmarkOperations.getAll((err, rows) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json(rows || []);
    });
});

// 添加快照收藏
app.post('/kz/api/snapshots', (req, res) => {
    const snapshot = req.body;

    if (!snapshot.title || !snapshot.url) {
        res.status(400).json({ error: '标题和网址是必填项' });
        return;
    }

    snapshotDb.snapshotBookmarkOperations.add(snapshot, (err, id) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json({ id, message: '快照收藏添加成功' });
    });
});

// 批量添加快照收藏
app.post('/kz/api/snapshots/batch', (req, res) => {
    const { snapshots } = req.body;

    if (!snapshots || !Array.isArray(snapshots) || snapshots.length === 0) {
        res.status(400).json({ error: '快照数据格式错误' });
        return;
    }

    snapshotDb.snapshotBookmarkOperations.addBatch(snapshots, (err) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json({ message: `成功添加 ${snapshots.length} 个快照收藏` });
    });
});

// 更新快照收藏
app.put('/kz/api/snapshots/:id', (req, res) => {
    const id = req.params.id;
    const snapshot = req.body;

    if (!snapshot.title || !snapshot.url) {
        res.status(400).json({ error: '标题和网址是必填项' });
        return;
    }

    snapshotDb.snapshotBookmarkOperations.update(id, snapshot, (err) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json({ message: '快照收藏更新成功' });
    });
});

// 删除快照收藏
app.delete('/kz/api/snapshots/:id', (req, res) => {
    const id = req.params.id;

    snapshotDb.snapshotBookmarkOperations.delete(id, (err) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json({ message: '快照收藏删除成功' });
    });
});

// 获取快照分类
app.get('/kz/api/categories', (req, res) => {
    snapshotDb.snapshotCategoryOperations.getAll((err, rows) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json(rows.map(row => row.category));
    });
});

// 获取快照状态
app.get('/kz/api/statuses', (req, res) => {
    snapshotDb.snapshotStatusOperations.getAll((err, rows) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json(rows.map(row => row.status));
    });
});

// 快照导出API
app.get('/kz/api/export', async (req, res) => {
    try {
        const filters = {
            search: req.query.search,
            category: req.query.category,
            status: req.query.status
        };

        const result = await snapshotAutoExport.manualExport(filters);
        res.json({
            success: true,
            message: '快照导出成功',
            fileName: result.fileName,
            count: result.count
        });
    } catch (error) {
        console.error('快照导出失败:', error);
        res.status(500).json({
            success: false,
            error: '快照导出失败',
            message: error.message
        });
    }
});

// 调试页面路由
app.get('/debug', (req, res) => {
    res.sendFile(path.join(__dirname, 'debug-snapshot.html'));
});

// 测试页面路由
app.get('/test', (req, res) => {
    res.sendFile(path.join(__dirname, 'test-snapshot.html'));
});

// 简单测试页面路由
app.get('/simple-test', (req, res) => {
    res.sendFile(path.join(__dirname, 'simple-test.html'));
});

// mShots测试页面路由
app.get('/test-mshots', (req, res) => {
    res.sendFile(path.join(__dirname, 'test-mshots.html'));
});

// JavaScript测试页面路由
app.get('/test-js', (req, res) => {
    res.sendFile(path.join(__dirname, 'test-js.html'));
});

// 主页路由
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`网址收藏系统运行在 http://localhost:${PORT}`);
});
