// 全局变量
let bookmarks = [];
let categories = [];
let isEditing = false;
let currentPage = 1;
let pageSize = 20;
let totalPages = 1;
let currentFilter = { type: 'all', value: '' }; // 记录当前的筛选条件

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initTheme();
    loadBookmarks();
    loadCategories();
    loadStatuses();
    loadExportStatus();
    setupEventListeners();
});

// 初始化主题
function initTheme() {
    // 从localStorage获取保存的主题，默认为浅色主题
    const savedTheme = localStorage.getItem('theme') || 'light';
    const html = document.documentElement;

    if (savedTheme === 'dark') {
        html.classList.add('dark');
    } else {
        html.classList.remove('dark');
    }
}

// 切换主题
function toggleTheme() {
    const html = document.documentElement;
    const isDark = html.classList.contains('dark');

    if (isDark) {
        html.classList.remove('dark');
        localStorage.setItem('theme', 'light');
    } else {
        html.classList.add('dark');
        localStorage.setItem('theme', 'dark');
    }
}

// 设置事件监听器
function setupEventListeners() {
    document.getElementById('editFormElement').addEventListener('submit', handleEditFormSubmit);
    document.getElementById('batchAddForm').addEventListener('submit', handleBatchAddSubmit);
    document.getElementById('exportForm').addEventListener('submit', handleExportSubmit);
    document.getElementById('searchInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchBookmarks();
        }
    });

    // 导出类型切换监听
    document.querySelectorAll('input[name="exportType"]').forEach(radio => {
        radio.addEventListener('change', function() {
            const filterOptions = document.getElementById('filterOptions');
            if (this.value === 'filtered') {
                filterOptions.style.display = 'block';
            } else {
                filterOptions.style.display = 'none';
            }
        });
    });

    // 批量输入框的键盘快捷键监听
    const batchInput = document.getElementById('batchInput');
    if (batchInput) {
        batchInput.addEventListener('keydown', function(e) {
            // Ctrl+V 时显示提示
            if (e.ctrlKey && e.key === 'v') {
                setTimeout(() => {
                    showToast('内容已粘贴', 'success', 1500);
                }, 100);
            }
        });
    }
}

// 加载所有收藏
async function loadBookmarks(page = 1) {
    try {
        currentPage = page;
        currentFilter = { type: 'all', value: '' };

        const response = await fetch(`/api/bookmarks?page=${page}&limit=${pageSize}`);
        const result = await response.json();

        bookmarks = result.data;
        renderBookmarks(bookmarks);
        renderPagination(result.pagination);
    } catch (error) {
        console.error('加载收藏失败:', error);
        showToast('加载收藏失败', 'error');
    }
}

// 加载分类
async function loadCategories() {
    try {
        const response = await fetch('/api/categories');
        categories = await response.json();
        updateCategoryFilter();
    } catch (error) {
        console.error('加载分类失败:', error);
    }
}

// 加载状态
async function loadStatuses() {
    try {
        const response = await fetch('/api/statuses');
        const statuses = await response.json();
        updateStatusFilter(statuses);
    } catch (error) {
        console.error('加载状态失败:', error);
    }
}

// 加载导出状态
async function loadExportStatus() {
    try {
        const response = await fetch('/api/export/info');
        const info = await response.json();
        updateExportStatus(info);
    } catch (error) {
        console.error('加载导出状态失败:', error);
        updateExportStatus({ exists: false });
    }
}

// 更新导出状态显示
function updateExportStatus(info) {
    const statusElement = document.getElementById('exportStatus');
    if (!statusElement) return;

    if (info.exists) {
        const fileSize = (info.size / 1024).toFixed(1);
        statusElement.innerHTML = `
            <div class="w-2 h-2 bg-green-500 rounded-full"></div>
            <span title="文件: ${info.file}&#10;大小: ${fileSize}KB&#10;更新: ${info.lastModified}">
                自动备份已启用 (${fileSize}KB)
            </span>
        `;
    } else {
        statusElement.innerHTML = `
            <div class="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
            <span>正在初始化备份...</span>
        `;
    }
}

// 更新分类筛选器
function updateCategoryFilter() {
    const select = document.getElementById('categoryFilter');
    select.innerHTML = '<option value="">所有分类</option>';

    categories.forEach(category => {
        const option = document.createElement('option');
        option.value = category;
        option.textContent = category;
        select.appendChild(option);
    });

    // 同时更新批量添加的分类下拉框
    updateBatchCategorySelect();

    // 同时更新导出弹窗的分类下拉框
    updateExportCategorySelect();
}

// 更新批量添加的分类下拉框
function updateBatchCategorySelect() {
    const select = document.getElementById('batchCategorySelect');
    if (!select) return;

    // 保存当前选中的值
    const currentValue = select.value;

    // 重新构建选项
    select.innerHTML = '<option value="">选择分类</option><option value="默认">默认</option>';

    categories.forEach(category => {
        if (category !== '默认') { // 避免重复添加"默认"
            const option = document.createElement('option');
            option.value = category;
            option.textContent = category;
            select.appendChild(option);
        }
    });

    // 恢复之前选中的值
    if (currentValue) {
        select.value = currentValue;
    }
}

// 分类下拉框变化时更新文本框
function updateBatchCategory() {
    const select = document.getElementById('batchCategorySelect');
    const input = document.getElementById('batchCategory');

    if (select.value) {
        input.value = select.value;
    }
}

// 更新导出弹窗的分类下拉框
function updateExportCategorySelect() {
    const select = document.getElementById('exportCategory');
    if (!select) return;

    // 保存当前选中的值
    const currentValue = select.value;

    // 重新构建选项
    select.innerHTML = '<option value="">所有分类</option>';

    categories.forEach(category => {
        const option = document.createElement('option');
        option.value = category;
        option.textContent = category;
        select.appendChild(option);
    });

    // 恢复之前选中的值
    if (currentValue) {
        select.value = currentValue;
    }
}

// 显示导出弹窗
function showExportModal() {
    document.getElementById('exportModal').style.display = 'flex';
    // 重置表单
    document.getElementById('exportForm').reset();
    document.getElementById('filterOptions').style.display = 'none';
    // 确保分类选项是最新的
    updateExportCategorySelect();
}

// 隐藏导出弹窗
function hideExportModal() {
    document.getElementById('exportModal').style.display = 'none';
}

// 处理导出表单提交
async function handleExportSubmit(e) {
    e.preventDefault();

    const exportType = document.querySelector('input[name="exportType"]:checked').value;
    const exportCategory = document.getElementById('exportCategory').value;
    const exportStatus = document.getElementById('exportStatus').value;

    try {
        let url = '/api/bookmarks?limit=10000'; // 获取大量数据用于导出

        if (exportType === 'filtered') {
            if (exportCategory) {
                url += `&category=${encodeURIComponent(exportCategory)}`;
            }
            if (exportStatus) {
                url += `&status=${encodeURIComponent(exportStatus)}`;
            }
        }

        showToast('正在准备导出数据...', 'info', 2000);

        const response = await fetch(url);
        const result = await response.json();
        const dataToExport = result.data || result; // 兼容分页和非分页数据

        if (dataToExport.length === 0) {
            showToast('没有数据可以导出', 'warning');
            return;
        }

        // 准备Excel数据
        const excelData = dataToExport.map(bookmark => ({
            '标题': bookmark.title,
            '网址': bookmark.url,
            '分类': bookmark.category,
            '状态': bookmark.status,
            '描述': bookmark.description || '',
            '创建时间': formatDateForExcel(bookmark.created_at),
            '更新时间': formatDateForExcel(bookmark.updated_at)
        }));

        // 创建工作簿
        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.json_to_sheet(excelData);

        // 设置列宽
        const colWidths = [
            { wch: 30 }, // 标题
            { wch: 50 }, // 网址
            { wch: 15 }, // 分类
            { wch: 10 }, // 状态
            { wch: 40 }, // 描述
            { wch: 20 }, // 创建时间
            { wch: 20 }  // 更新时间
        ];
        ws['!cols'] = colWidths;

        // 添加工作表到工作簿
        XLSX.utils.book_append_sheet(wb, ws, '网址收藏');

        // 生成文件名
        const now = new Date();
        const dateStr = now.toISOString().slice(0, 10);
        let fileName = `网址收藏_${dateStr}`;

        if (exportType === 'filtered') {
            if (exportCategory) fileName += `_${exportCategory}`;
            if (exportStatus) fileName += `_${exportStatus}`;
        }
        fileName += '.xlsx';

        // 下载文件
        XLSX.writeFile(wb, fileName);

        hideExportModal();
        showToast(`成功导出 ${dataToExport.length} 条收藏记录`, 'success', 4000);

    } catch (error) {
        console.error('导出失败:', error);
        showToast('导出失败，请重试', 'error');
    }
}

// 格式化日期用于Excel
function formatDateForExcel(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

// 更新状态筛选器
function updateStatusFilter(statuses) {
    const select = document.getElementById('statusFilter');
    // 保持现有的选项，只添加数据库中存在的状态
    const existingOptions = ['默认', '未读', '已读', '收藏', '临时'];

    // 添加数据库中存在但不在默认列表中的状态
    statuses.forEach(status => {
        if (!existingOptions.includes(status)) {
            const option = document.createElement('option');
            option.value = status;
            option.textContent = status;
            select.appendChild(option);
        }
    });
}

// 获取状态对应的颜色类
function getStatusColor(status) {
    switch (status) {
        case '未读':
            return 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200';
        case '已读':
            return 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200';
        case '收藏':
            return 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200';
        case '临时':
            return 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200';
        case '默认':
        default:
            return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200';
    }
}

// 从URL提取域名
function extractDomain(url) {
    try {
        const urlObj = new URL(url);
        return urlObj.hostname;
    } catch (error) {
        return null;
    }
}

// 根据域名获取MDI图标
function getMDIIconForDomain(domain) {
    const domainIconMap = {
        // 搜索引擎
        'google.com': 'mdi-google',
        'baidu.com': 'mdi-web',
        'bing.com': 'mdi-microsoft',
        'yahoo.com': 'mdi-yahoo',
        'duckduckgo.com': 'mdi-duck',

        // 社交媒体
        'facebook.com': 'mdi-facebook',
        'twitter.com': 'mdi-twitter',
        'instagram.com': 'mdi-instagram',
        'linkedin.com': 'mdi-linkedin',
        'youtube.com': 'mdi-youtube',
        'tiktok.com': 'mdi-music-note',
        'weibo.com': 'mdi-sina-weibo',
        'zhihu.com': 'mdi-forum',

        // 开发工具
        'github.com': 'mdi-github',
        'gitlab.com': 'mdi-gitlab',
        'stackoverflow.com': 'mdi-stack-overflow',
        'npmjs.com': 'mdi-npm',
        'docker.com': 'mdi-docker',
        'kubernetes.io': 'mdi-kubernetes',

        // 云服务
        'aws.amazon.com': 'mdi-aws',
        'azure.microsoft.com': 'mdi-microsoft-azure',
        'cloud.google.com': 'mdi-google-cloud',
        'digitalocean.com': 'mdi-digital-ocean',

        // 购物
        'amazon.com': 'mdi-amazon',
        'taobao.com': 'mdi-shopping',
        'tmall.com': 'mdi-shopping',
        'jd.com': 'mdi-shopping-outline',
        'ebay.com': 'mdi-shopping',

        // 新闻媒体
        'reddit.com': 'mdi-reddit',
        'medium.com': 'mdi-medium',
        'wikipedia.org': 'mdi-wikipedia',
        'news.ycombinator.com': 'mdi-hacker-news',

        // 工具
        'notion.so': 'mdi-note-text',
        'trello.com': 'mdi-trello',
        'slack.com': 'mdi-slack',
        'discord.com': 'mdi-discord',
        'zoom.us': 'mdi-video',
        'figma.com': 'mdi-vector-square',

        // 邮箱
        'gmail.com': 'mdi-gmail',
        'outlook.com': 'mdi-microsoft-outlook',
        'mail.yahoo.com': 'mdi-yahoo',

        // 音乐视频
        'spotify.com': 'mdi-spotify',
        'apple.com': 'mdi-apple',
        'netflix.com': 'mdi-netflix',
        'bilibili.com': 'mdi-play-circle',

        // 学习
        'coursera.org': 'mdi-school',
        'udemy.com': 'mdi-school',
        'khan.academy.org': 'mdi-school',
        'edx.org': 'mdi-school'
    };

    // 检查完整域名
    if (domainIconMap[domain]) {
        return domainIconMap[domain];
    }

    // 检查主域名（去掉子域名）
    const mainDomain = domain.split('.').slice(-2).join('.');
    if (domainIconMap[mainDomain]) {
        return domainIconMap[mainDomain];
    }

    // 根据域名类型返回通用图标
    if (domain.includes('blog') || domain.includes('wordpress')) {
        return 'mdi-post';
    } else if (domain.includes('wiki')) {
        return 'mdi-wikipedia';
    } else if (domain.includes('news')) {
        return 'mdi-newspaper';
    } else if (domain.includes('shop') || domain.includes('store')) {
        return 'mdi-shopping';
    } else if (domain.includes('mail')) {
        return 'mdi-email';
    } else if (domain.includes('video') || domain.includes('tv')) {
        return 'mdi-play-circle';
    } else if (domain.includes('music')) {
        return 'mdi-music';
    } else if (domain.includes('photo') || domain.includes('image')) {
        return 'mdi-image';
    } else if (domain.includes('doc') || domain.includes('pdf')) {
        return 'mdi-file-document';
    }

    return null; // 没有匹配的MDI图标
}

// 生成彩色SVG字母图标
function generateColorfulSVGIcon(title, domain) {
    const firstLetter = (title || domain || 'W').charAt(0).toUpperCase();

    // 颜色方案
    const colorSchemes = [
        { bg: '#FF6B6B', text: '#FFFFFF' }, // 红色
        { bg: '#4ECDC4', text: '#FFFFFF' }, // 青色
        { bg: '#45B7D1', text: '#FFFFFF' }, // 蓝色
        { bg: '#96CEB4', text: '#FFFFFF' }, // 绿色
        { bg: '#FFEAA7', text: '#2D3436' }, // 黄色
        { bg: '#DDA0DD', text: '#FFFFFF' }, // 紫色
        { bg: '#98D8C8', text: '#2D3436' }, // 薄荷绿
        { bg: '#F7DC6F', text: '#2D3436' }, // 金色
        { bg: '#BB8FCE', text: '#FFFFFF' }, // 淡紫色
        { bg: '#85C1E9', text: '#FFFFFF' }, // 天蓝色
        { bg: '#F8C471', text: '#2D3436' }, // 橙色
        { bg: '#82E0AA', text: '#2D3436' }  // 浅绿色
    ];

    // 根据首字母选择颜色（保证同样的字母总是同样的颜色）
    const colorIndex = firstLetter.charCodeAt(0) % colorSchemes.length;
    const colors = colorSchemes[colorIndex];

    // 生成SVG
    const svg = `
        <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="grad-${firstLetter}" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:${colors.bg};stop-opacity:1" />
                    <stop offset="100%" style="stop-color:${adjustBrightness(colors.bg, -20)};stop-opacity:1" />
                </linearGradient>
            </defs>
            <circle cx="12" cy="12" r="11" fill="url(#grad-${firstLetter})" stroke="${adjustBrightness(colors.bg, -30)}" stroke-width="1"/>
            <text x="12" y="17" font-family="Arial, sans-serif" font-size="12" font-weight="bold" text-anchor="middle" fill="${colors.text}">
                ${firstLetter}
            </text>
        </svg>
    `;

    return `data:image/svg+xml;base64,${btoa(svg)}`;
}

// 调整颜色亮度
function adjustBrightness(hex, percent) {
    // 移除 # 号
    hex = hex.replace('#', '');

    // 转换为 RGB
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);

    // 调整亮度
    const newR = Math.max(0, Math.min(255, r + (r * percent / 100)));
    const newG = Math.max(0, Math.min(255, g + (g * percent / 100)));
    const newB = Math.max(0, Math.min(255, b + (b * percent / 100)));

    // 转换回十六进制
    return `#${Math.round(newR).toString(16).padStart(2, '0')}${Math.round(newG).toString(16).padStart(2, '0')}${Math.round(newB).toString(16).padStart(2, '0')}`;
}

// 加载网站图标
async function loadFavicons(bookmarks) {
    if (!bookmarks || bookmarks.length === 0) return;

    // 为每个书签生成图标
    bookmarks.forEach(bookmark => {
        const domain = extractDomain(bookmark.url);
        const containerElement = document.getElementById(`favicon-${bookmark.id}`)?.parentElement;

        if (!containerElement) return;

        // 首先尝试使用MDI图标
        const mdiIcon = getMDIIconForDomain(domain);

        if (mdiIcon) {
            // 使用MDI图标
            const iconColor = getIconColorForDomain(domain);
            containerElement.innerHTML = `
                <i class="mdi ${mdiIcon} text-lg" style="color: ${iconColor};" title="${bookmark.title}"></i>
            `;
        } else {
            // 使用彩色SVG字母图标
            const svgIcon = generateColorfulSVGIcon(bookmark.title, domain);
            containerElement.innerHTML = `
                <img src="${svgIcon}"
                     alt="${bookmark.title}"
                     class="w-6 h-6 rounded"
                     title="${bookmark.title}">
            `;
        }
    });
}

// 为域名获取图标颜色
function getIconColorForDomain(domain) {
    const colorMap = {
        'google.com': '#4285F4',
        'github.com': '#181717',
        'youtube.com': '#FF0000',
        'facebook.com': '#1877F2',
        'twitter.com': '#1DA1F2',
        'instagram.com': '#E4405F',
        'linkedin.com': '#0A66C2',
        'reddit.com': '#FF4500',
        'stackoverflow.com': '#F58025',
        'medium.com': '#000000',
        'spotify.com': '#1DB954',
        'netflix.com': '#E50914',
        'amazon.com': '#FF9900',
        'microsoft.com': '#00BCF2',
        'apple.com': '#000000',
        'slack.com': '#4A154B',
        'discord.com': '#5865F2',
        'figma.com': '#F24E1E',
        'notion.so': '#000000',
        'trello.com': '#0079BF'
    };

    // 检查完整域名
    if (colorMap[domain]) {
        return colorMap[domain];
    }

    // 检查主域名
    const mainDomain = domain.split('.').slice(-2).join('.');
    if (colorMap[mainDomain]) {
        return colorMap[mainDomain];
    }

    // 默认颜色
    return '#6B7280';
}

// 渲染收藏列表
function renderBookmarks(bookmarksToRender) {
    const container = document.getElementById('bookmarksList');

    if (bookmarksToRender.length === 0) {
        container.innerHTML = `
            <div class="flex flex-col items-center justify-center py-16 px-6">
                <svg class="w-16 h-16 text-gray-300 dark:text-gray-600 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">暂无收藏</h3>
                <p class="text-gray-500 dark:text-gray-400 text-center">点击"添加收藏"开始收藏你喜欢的网站吧！</p>
            </div>
        `;
        return;
    }

    container.innerHTML = `
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">标题</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">访问</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">分类</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">状态</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">描述</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">操作</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    ${bookmarksToRender.map(bookmark => `
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 w-8 h-8 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center mr-3 overflow-hidden">
                                        <img id="favicon-${bookmark.id}"
                                             src=""
                                             alt=""
                                             class="w-6 h-6 rounded hidden"
                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                        <svg class="w-4 h-4 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                                        </svg>
                                    </div>
                                    <div class="min-w-0 flex-1">
                                        <div class="text-sm font-medium text-gray-900 dark:text-white truncate max-w-xs" title="${escapeHtml(bookmark.title)}">
                                            ${escapeHtml(bookmark.title)}
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-center">
                                <a href="${bookmark.url}" target="_blank"
                                   class="inline-flex items-center justify-center w-8 h-8 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-all duration-200"
                                   title="访问：${bookmark.url}">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                    </svg>
                                </a>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">
                                    ${escapeHtml(bookmark.category)}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(bookmark.status)}">
                                    ${escapeHtml(bookmark.status || '默认')}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 max-w-xs">
                                <div class="truncate" title="${bookmark.description ? escapeHtml(bookmark.description) : ''}">
                                    ${bookmark.description ? escapeHtml(bookmark.description) : '-'}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex items-center justify-end gap-2">
                                    <button onclick="copyBookmark(${bookmark.id})"
                                            class="text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300 transition-colors" title="复制">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                        </svg>
                                    </button>
                                    <button onclick="editBookmark(${bookmark.id})"
                                            class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 transition-colors" title="编辑">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                    </button>
                                    <button onclick="deleteBookmark(${bookmark.id})"
                                            class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 transition-colors" title="删除">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;

    // 渲染完成后加载图标
    loadFavicons(bookmarksToRender);
}

// 渲染分页控件
function renderPagination(pagination) {
    const paginationContainer = document.getElementById('paginationContainer');
    const pageInfo = document.getElementById('pageInfo');
    const totalInfo = document.getElementById('totalInfo');
    const pageNumbers = document.getElementById('pageNumbers');
    const prevBtn = document.getElementById('prevPageBtn');
    const nextBtn = document.getElementById('nextPageBtn');
    const countElement = document.getElementById('bookmarkCount');

    if (!pagination || pagination.total === 0) {
        paginationContainer.style.display = 'none';
        countElement.textContent = '共 0 个收藏';
        return;
    }

    // 显示分页控件
    paginationContainer.style.display = 'block';

    // 更新总数信息
    totalPages = pagination.totalPages;
    const startItem = (pagination.page - 1) * pagination.limit + 1;
    const endItem = Math.min(pagination.page * pagination.limit, pagination.total);

    pageInfo.textContent = `${startItem}-${endItem}`;
    totalInfo.textContent = pagination.total;
    countElement.textContent = `共 ${pagination.total} 个收藏`;

    // 更新上一页/下一页按钮状态
    prevBtn.disabled = !pagination.hasPrev;
    nextBtn.disabled = !pagination.hasNext;

    // 生成页码按钮
    pageNumbers.innerHTML = '';

    // 计算显示的页码范围
    let startPage = Math.max(1, pagination.page - 2);
    let endPage = Math.min(pagination.totalPages, pagination.page + 2);

    // 如果总页数较少，显示所有页码
    if (pagination.totalPages <= 5) {
        startPage = 1;
        endPage = pagination.totalPages;
    }

    // 添加第一页和省略号
    if (startPage > 1) {
        pageNumbers.appendChild(createPageButton(1, pagination.page));
        if (startPage > 2) {
            pageNumbers.appendChild(createEllipsis());
        }
    }

    // 添加页码按钮
    for (let i = startPage; i <= endPage; i++) {
        pageNumbers.appendChild(createPageButton(i, pagination.page));
    }

    // 添加省略号和最后一页
    if (endPage < pagination.totalPages) {
        if (endPage < pagination.totalPages - 1) {
            pageNumbers.appendChild(createEllipsis());
        }
        pageNumbers.appendChild(createPageButton(pagination.totalPages, pagination.page));
    }
}

// 创建页码按钮
function createPageButton(pageNum, currentPageNum) {
    const button = document.createElement('button');
    button.textContent = pageNum;
    button.onclick = () => goToPage(pageNum);

    const baseClasses = 'px-3 py-1 border rounded text-sm transition-colors';
    if (pageNum === currentPageNum) {
        button.className = `${baseClasses} bg-primary text-white border-primary`;
    } else {
        button.className = `${baseClasses} border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600`;
    }

    return button;
}

// 创建省略号
function createEllipsis() {
    const span = document.createElement('span');
    span.textContent = '...';
    span.className = 'px-2 text-gray-500 dark:text-gray-400';
    return span;
}

// 从剪切板粘贴内容
async function pasteFromClipboard() {
    try {
        // 检查是否支持现代剪切板API
        if (navigator.clipboard && window.isSecureContext) {
            const text = await navigator.clipboard.readText();
            if (text) {
                const batchInput = document.getElementById('batchInput');
                const currentValue = batchInput.value;

                // 如果文本框已有内容，询问是否替换
                if (currentValue.trim()) {
                    const shouldReplace = confirm('文本框中已有内容，是否要替换为剪切板内容？\n\n点击"确定"替换，点击"取消"追加到末尾。');
                    if (shouldReplace) {
                        batchInput.value = text;
                    } else {
                        // 追加到末尾，确保换行
                        batchInput.value = currentValue + (currentValue.endsWith('\n') ? '' : '\n') + text;
                    }
                } else {
                    batchInput.value = text;
                }

                // 聚焦到文本框
                batchInput.focus();

                // 显示成功提示
                showToast('剪切板内容已粘贴', 'success', 2000);
            } else {
                showToast('剪切板为空', 'warning');
            }
        } else {
            // 降级方案：提示用户手动粘贴
            showToast('请使用 Ctrl+V 手动粘贴', 'info', 3000);
            document.getElementById('batchInput').focus();
        }
    } catch (error) {
        console.error('读取剪切板失败:', error);

        // 检查是否是权限问题
        if (error.name === 'NotAllowedError') {
            showToast('需要剪切板权限，请使用 Ctrl+V 手动粘贴', 'warning', 4000);
        } else {
            showToast('读取剪切板失败，请使用 Ctrl+V 手动粘贴', 'error', 4000);
        }

        // 聚焦到文本框，方便用户手动粘贴
        document.getElementById('batchInput').focus();
    }
}

// 显示批量添加弹窗
function showBatchAddModal() {
    document.getElementById('batchAddModal').style.display = 'flex';
    document.getElementById('batchInput').focus();
}

// 隐藏批量添加弹窗
function hideBatchAddModal() {
    document.getElementById('batchAddModal').style.display = 'none';
    document.getElementById('batchAddForm').reset();
    // 重置分类选择
    document.getElementById('batchCategorySelect').value = '';
    document.getElementById('batchCategory').value = '';
    // 重置状态为"未读"
    document.getElementById('batchStatus').value = '未读';
}

// 编辑收藏
function editBookmark(id) {
    const bookmark = bookmarks.find(b => b.id === id);
    if (!bookmark) return;

    document.getElementById('editBookmarkId').value = bookmark.id;
    document.getElementById('editTitle').value = bookmark.title;
    document.getElementById('editUrl').value = bookmark.url;
    document.getElementById('editDescription').value = bookmark.description || '';
    document.getElementById('editCategory').value = bookmark.category;
    document.getElementById('editStatus').value = bookmark.status || '默认';
    document.getElementById('editForm').style.display = 'block';
    document.getElementById('editTitle').focus();
}

// 隐藏编辑表单
function hideEditForm() {
    document.getElementById('editForm').style.display = 'none';
    document.getElementById('editFormElement').reset();
}

// 处理编辑表单提交
async function handleEditFormSubmit(e) {
    e.preventDefault();

    const formData = {
        title: document.getElementById('editTitle').value.trim(),
        url: document.getElementById('editUrl').value.trim(),
        description: document.getElementById('editDescription').value.trim(),
        category: document.getElementById('editCategory').value.trim() || '默认',
        status: document.getElementById('editStatus').value || '默认'
    };

    if (!formData.title || !formData.url) {
        showToast('标题和网址是必填项', 'warning');
        return;
    }

    try {
        const id = document.getElementById('editBookmarkId').value;
        const response = await fetch(`/api/bookmarks/${id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(formData)
        });

        if (response.ok) {
            hideEditForm();
            loadBookmarks();
            loadCategories();
            // 延迟更新导出状态，等待自动导出完成（3分钟+10秒缓冲）
            setTimeout(() => loadExportStatus(), 3 * 60 * 1000 + 10000);
            showToast('收藏更新成功', 'success');
        } else {
            const error = await response.json();
            showToast(error.error || '操作失败', 'error');
        }
    } catch (error) {
        console.error('操作失败:', error);
        showToast('操作失败', 'error');
    }
}

// 处理批量添加表单提交
async function handleBatchAddSubmit(e) {
    e.preventDefault();

    const batchInput = document.getElementById('batchInput').value.trim();
    const batchCategory = document.getElementById('batchCategory').value.trim() ||
                         document.getElementById('batchCategorySelect').value || '默认';
    const batchStatus = document.getElementById('batchStatus').value || '默认';

    if (!batchInput) {
        showToast('请输入要添加的收藏信息', 'warning');
        return;
    }

    // 解析输入的数据
    const lines = batchInput.split('\n').filter(line => line.trim());
    const bookmarksToAdd = [];
    const errors = [];

    lines.forEach((line, index) => {
        const parts = line.split('|').map(part => part.trim());

        if (parts.length < 2) {
            errors.push(`第 ${index + 1} 行：格式错误，至少需要标题和网址`);
            return;
        }

        const [title, url, description] = parts;

        if (!title || !url) {
            errors.push(`第 ${index + 1} 行：标题和网址不能为空`);
            return;
        }

        bookmarksToAdd.push({
            title,
            url,
            description: description || '',
            category: batchCategory,
            status: batchStatus
        });
    });

    if (errors.length > 0) {
        showToast('发现格式错误，请检查输入内容', 'error', 5000);
        return;
    }

    if (bookmarksToAdd.length === 0) {
        showToast('没有有效的收藏数据', 'warning');
        return;
    }

    // 批量添加收藏
    let successCount = 0;
    let failCount = 0;
    const failedItems = [];

    for (let i = 0; i < bookmarksToAdd.length; i++) {
        try {
            const response = await fetch('/api/bookmarks', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(bookmarksToAdd[i])
            });

            if (response.ok) {
                successCount++;
            } else {
                failCount++;
                const error = await response.json();
                failedItems.push(`${bookmarksToAdd[i].title}: ${error.error}`);
            }
        } catch (error) {
            failCount++;
            failedItems.push(`${bookmarksToAdd[i].title}: 网络错误`);
        }
    }

    // 显示结果
    if (successCount > 0 && failCount === 0) {
        showToast(`批量添加成功！共添加 ${successCount} 个收藏`, 'success', 4000);
        hideBatchAddModal();
        loadBookmarks();
        loadCategories();
        // 延迟更新导出状态，等待自动导出完成（3分钟+10秒缓冲）
        setTimeout(() => loadExportStatus(), 3 * 60 * 1000 + 10000);
    } else if (successCount > 0 && failCount > 0) {
        showToast(`批量添加完成！成功 ${successCount} 个，失败 ${failCount} 个`, 'warning', 5000);
        hideBatchAddModal();
        loadBookmarks();
        loadCategories();
        // 延迟更新导出状态，等待自动导出完成（3分钟+10秒缓冲）
        setTimeout(() => loadExportStatus(), 3 * 60 * 1000 + 10000);
    } else {
        showToast(`批量添加失败！失败 ${failCount} 个`, 'error', 4000);
    }
}

// 复制收藏
async function copyBookmark(id) {
    const bookmark = bookmarks.find(b => b.id === id);
    if (!bookmark) return;

    // 构建复制内容：标题|网址||描述
    const copyText = `${bookmark.title}|${bookmark.url}||${bookmark.description || ''}`;

    try {
        // 使用现代的 Clipboard API
        if (navigator.clipboard && window.isSecureContext) {
            await navigator.clipboard.writeText(copyText);
            showCopySuccess(bookmark.title);
        } else {
            // 降级方案：使用传统的 document.execCommand
            const textArea = document.createElement('textarea');
            textArea.value = copyText;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                document.execCommand('copy');
                showCopySuccess(bookmark.title);
            } catch (err) {
                console.error('复制失败:', err);
                showToast('复制失败，请手动复制', 'error');
            } finally {
                document.body.removeChild(textArea);
            }
        }
    } catch (error) {
        console.error('复制失败:', error);
        showToast('复制失败，请手动复制', 'error');
    }
}

// 通用 Toast 通知函数
function showToast(message, type = 'success', duration = 3000) {
    // 创建提示元素
    const toast = document.createElement('div');

    // 根据类型设置不同的样式
    let bgColor = '';
    let icon = '';

    switch (type) {
        case 'success':
            bgColor = 'bg-green-500';
            icon = `<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>`;
            break;
        case 'error':
            bgColor = 'bg-red-500';
            icon = `<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>`;
            break;
        case 'warning':
            bgColor = 'bg-yellow-500';
            icon = `<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>`;
            break;
        case 'info':
            bgColor = 'bg-blue-500';
            icon = `<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>`;
            break;
    }

    toast.className = `fixed top-4 right-4 ${bgColor} text-white px-4 py-3 rounded-lg shadow-lg z-50 transition-all duration-300 transform translate-x-full max-w-sm`;
    toast.innerHTML = `
        <div class="flex items-center">
            ${icon}
            <span class="text-sm font-medium">${message}</span>
        </div>
    `;

    document.body.appendChild(toast);

    // 显示动画
    setTimeout(() => {
        toast.classList.remove('translate-x-full');
    }, 100);

    // 指定时间后隐藏
    setTimeout(() => {
        toast.classList.add('translate-x-full');
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, duration);
}

// 显示复制成功提示
function showCopySuccess(title) {
    showToast(`复制【${title}】成功`, 'success');
}

// 显示自定义确认弹窗
function showConfirmDialog(title, message, onConfirm, onCancel = null) {
    // 创建弹窗HTML
    const dialogHTML = `
        <div id="confirmDialog" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 animate-fadeIn">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4 animate-scaleIn">
                <!-- 头部 -->
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <div class="flex items-center gap-3">
                        <div class="w-8 h-8 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">${title}</h3>
                    </div>
                </div>

                <!-- 内容 -->
                <div class="px-6 py-4">
                    <p class="text-gray-600 dark:text-gray-300 leading-relaxed">${message}</p>
                </div>

                <!-- 按钮 -->
                <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex justify-end gap-3">
                    <button id="confirmCancel" class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors">
                        取消
                    </button>
                    <button id="confirmDelete" class="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-lg transition-colors">
                        删除
                    </button>
                </div>
            </div>
        </div>
    `;

    // 添加CSS动画样式
    if (!document.getElementById('confirmDialogStyles')) {
        const styles = document.createElement('style');
        styles.id = 'confirmDialogStyles';
        styles.textContent = `
            .animate-fadeIn {
                animation: fadeIn 0.2s ease-out;
            }
            .animate-scaleIn {
                animation: scaleIn 0.2s ease-out;
            }
            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }
            @keyframes scaleIn {
                from { opacity: 0; transform: scale(0.9); }
                to { opacity: 1; transform: scale(1); }
            }
        `;
        document.head.appendChild(styles);
    }

    // 添加弹窗到页面
    document.body.insertAdjacentHTML('beforeend', dialogHTML);

    const dialog = document.getElementById('confirmDialog');
    const cancelBtn = document.getElementById('confirmCancel');
    const deleteBtn = document.getElementById('confirmDelete');

    // 取消按钮事件
    const handleCancel = () => {
        dialog.remove();
        if (onCancel) onCancel();
    };

    // 确认按钮事件
    const handleConfirm = () => {
        dialog.remove();
        if (onConfirm) onConfirm();
    };

    // 绑定事件
    cancelBtn.addEventListener('click', handleCancel);
    deleteBtn.addEventListener('click', handleConfirm);

    // 点击背景关闭
    dialog.addEventListener('click', (e) => {
        if (e.target === dialog) {
            handleCancel();
        }
    });

    // ESC键关闭
    const handleKeydown = (e) => {
        if (e.key === 'Escape') {
            handleCancel();
            document.removeEventListener('keydown', handleKeydown);
        }
    };
    document.addEventListener('keydown', handleKeydown);
}

// 删除收藏
async function deleteBookmark(id) {
    // 从当前书签列表中找到要删除的项目
    const bookmark = bookmarks.find(b => b.id === id);
    const title = bookmark ? bookmark.title : '未知项目';

    showConfirmDialog(
        '确认删除',
        `确定要删除收藏【${title}】吗？删除后将无法恢复。`,
        async () => {
            try {
                const response = await fetch(`/api/bookmarks/${id}`, {
                    method: 'DELETE'
                });

                if (response.ok) {
                    loadBookmarks();
                    loadCategories();
                    // 延迟更新导出状态，等待自动导出完成（3分钟+10秒缓冲）
                    setTimeout(() => loadExportStatus(), 3 * 60 * 1000 + 10000);
                    showToast(`收藏【${title}】删除成功`, 'success');
                } else {
                    showToast('删除失败', 'error');
                }
            } catch (error) {
                console.error('删除失败:', error);
                showToast('删除失败', 'error');
            }
        }
    );
}

// 跳转到指定页面
async function goToPage(page) {
    if (page < 1 || page > totalPages) return;

    try {
        let url = `/api/bookmarks?page=${page}&limit=${pageSize}`;

        // 根据当前筛选条件构建URL
        if (currentFilter.type === 'search') {
            url += `&search=${encodeURIComponent(currentFilter.value)}`;
        } else if (currentFilter.type === 'category') {
            url += `&category=${encodeURIComponent(currentFilter.value)}`;
        } else if (currentFilter.type === 'status') {
            url += `&status=${encodeURIComponent(currentFilter.value)}`;
        }

        const response = await fetch(url);
        const result = await response.json();

        currentPage = page;
        bookmarks = result.data;
        renderBookmarks(bookmarks);
        renderPagination(result.pagination);
    } catch (error) {
        console.error('加载页面失败:', error);
        showToast('加载页面失败', 'error');
    }
}

// 改变每页显示数量
async function changePageSize() {
    const newPageSize = parseInt(document.getElementById('pageSizeSelect').value);
    pageSize = newPageSize;

    // 重新加载第一页
    if (currentFilter.type === 'search') {
        searchBookmarks(1);
    } else if (currentFilter.type === 'category') {
        filterByCategory(1);
    } else if (currentFilter.type === 'status') {
        filterByStatus(1);
    } else {
        loadBookmarks(1);
    }
}

// 搜索收藏
async function searchBookmarks(page = 1) {
    const keyword = document.getElementById('searchInput').value.trim();
    if (!keyword) {
        showAllBookmarks();
        return;
    }

    try {
        currentPage = page;
        currentFilter = { type: 'search', value: keyword };

        const response = await fetch(`/api/bookmarks?search=${encodeURIComponent(keyword)}&page=${page}&limit=${pageSize}`);
        const result = await response.json();

        bookmarks = result.data;
        renderBookmarks(bookmarks);
        renderPagination(result.pagination);
    } catch (error) {
        console.error('搜索失败:', error);
        showToast('搜索失败', 'error');
    }
}

// 显示所有收藏
function showAllBookmarks() {
    document.getElementById('searchInput').value = '';
    document.getElementById('categoryFilter').value = '';
    document.getElementById('statusFilter').value = '';
    loadBookmarks(1);
}

// 按分类筛选
async function filterByCategory(page = 1) {
    const category = document.getElementById('categoryFilter').value;
    if (!category) {
        showAllBookmarks();
        return;
    }

    try {
        currentPage = page;
        currentFilter = { type: 'category', value: category };

        const response = await fetch(`/api/bookmarks?category=${encodeURIComponent(category)}&page=${page}&limit=${pageSize}`);
        const result = await response.json();

        bookmarks = result.data;
        renderBookmarks(bookmarks);
        renderPagination(result.pagination);
    } catch (error) {
        console.error('筛选失败:', error);
        showToast('筛选失败', 'error');
    }
}

// 按状态筛选
async function filterByStatus(page = 1) {
    const status = document.getElementById('statusFilter').value;
    if (!status) {
        showAllBookmarks();
        return;
    }

    try {
        currentPage = page;
        currentFilter = { type: 'status', value: status };

        const response = await fetch(`/api/bookmarks?status=${encodeURIComponent(status)}&page=${page}&limit=${pageSize}`);
        const result = await response.json();

        bookmarks = result.data;
        renderBookmarks(bookmarks);
        renderPagination(result.pagination);
    } catch (error) {
        console.error('状态筛选失败:', error);
        showToast('状态筛选失败', 'error');
    }
}

// 工具函数
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
    });
}
