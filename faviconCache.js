const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const https = require('https');
const http = require('http');

// 创建图标缓存数据库连接
const cacheDbPath = path.join(__dirname, 'favicon_cache.db');
const cacheDb = new sqlite3.Database(cacheDbPath);

// 初始化图标缓存表
function initFaviconCache() {
    const createTableSQL = `
        CREATE TABLE IF NOT EXISTS favicon_cache (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            domain TEXT UNIQUE NOT NULL,
            favicon_url TEXT,
            cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            expires_at DATETIME NOT NULL,
            status TEXT DEFAULT 'success'
        )
    `;

    cacheDb.run(createTableSQL, (err) => {
        if (err) {
            console.error('创建图标缓存表失败:', err.message);
        } else {
            console.log('图标缓存表初始化成功');
        }
    });
}

// 从URL提取域名
function extractDomain(url) {
    try {
        const urlObj = new URL(url);
        return urlObj.hostname;
    } catch (error) {
        console.error('提取域名失败:', url, error);
        return null;
    }
}

// 获取缓存的图标
function getCachedFavicon(domain, callback) {
    const sql = `
        SELECT favicon_url, expires_at, status 
        FROM favicon_cache 
        WHERE domain = ? AND expires_at > datetime('now')
    `;
    
    cacheDb.get(sql, [domain], (err, row) => {
        if (err) {
            console.error('查询图标缓存失败:', err);
            callback(err, null);
            return;
        }
        
        callback(null, row);
    });
}

// 保存图标到缓存
function saveFaviconToCache(domain, faviconUrl, status = 'success') {
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 30); // 30天后过期
    
    const sql = `
        INSERT OR REPLACE INTO favicon_cache (domain, favicon_url, expires_at, status)
        VALUES (?, ?, ?, ?)
    `;
    
    cacheDb.run(sql, [domain, faviconUrl, expiresAt.toISOString(), status], (err) => {
        if (err) {
            console.error('保存图标缓存失败:', err);
        } else {
            console.log(`图标缓存已保存: ${domain} -> ${faviconUrl}`);
        }
    });
}

// 通过多种服务获取网站图标
function fetchFaviconFromServices(domain) {
    return new Promise((resolve, reject) => {
        // 多个图标服务备选方案
        const faviconServices = [
            `https://www.google.com/s2/favicons?domain=${domain}&sz=32`,
            `https://favicon.im/${domain}?larger=true`,
            `https://icons.duckduckgo.com/ip3/${domain}.ico`,
            `https://${domain}/favicon.ico`
        ];

        // 添加随机延迟（1-3秒）
        const delay = Math.floor(Math.random() * 2000) + 1000;

        setTimeout(async () => {
            // 尝试每个服务
            for (const serviceUrl of faviconServices) {
                try {
                    const success = await testFaviconUrl(serviceUrl);
                    if (success) {
                        resolve(serviceUrl);
                        return;
                    }
                } catch (error) {
                    console.log(`图标服务失败 ${serviceUrl}:`, error.message);
                }
            }

            // 所有服务都失败
            resolve(null);
        }, delay);
    });
}

// 测试图标URL是否有效
function testFaviconUrl(url) {
    return new Promise((resolve) => {
        const urlObj = new URL(url);
        const client = urlObj.protocol === 'https:' ? https : http;

        const request = client.get(url, (response) => {
            if (response.statusCode === 200) {
                const contentType = response.headers['content-type'];
                if (contentType && (contentType.startsWith('image/') || contentType.includes('icon'))) {
                    resolve(true);
                } else {
                    resolve(false);
                }
            } else {
                resolve(false);
            }
            response.destroy();
        });

        request.on('error', () => {
            resolve(false);
        });

        request.setTimeout(8000, () => {
            request.destroy();
            resolve(false);
        });
    });
}

// 获取网站图标（带缓存）
async function getFavicon(url) {
    const domain = extractDomain(url);
    if (!domain) {
        return null;
    }
    
    return new Promise((resolve, reject) => {
        // 先检查缓存
        getCachedFavicon(domain, async (err, cached) => {
            if (err) {
                resolve(null);
                return;
            }
            
            if (cached) {
                // 缓存命中
                if (cached.status === 'success' && cached.favicon_url) {
                    resolve(cached.favicon_url);
                } else {
                    resolve(null); // 之前获取失败的记录
                }
                return;
            }
            
            // 缓存未命中，异步获取图标
            try {
                const faviconUrl = await fetchFaviconFromServices(domain);
                
                if (faviconUrl) {
                    saveFaviconToCache(domain, faviconUrl, 'success');
                    resolve(faviconUrl);
                } else {
                    saveFaviconToCache(domain, null, 'failed');
                    resolve(null);
                }
            } catch (error) {
                console.error('获取图标异常:', error);
                saveFaviconToCache(domain, null, 'failed');
                resolve(null);
            }
        });
    });
}

// 批量获取图标
async function getFavicons(urls) {
    const results = {};
    
    // 并发获取，但限制并发数量
    const batchSize = 3;
    for (let i = 0; i < urls.length; i += batchSize) {
        const batch = urls.slice(i, i + batchSize);
        const promises = batch.map(async (url) => {
            const favicon = await getFavicon(url);
            return { url, favicon };
        });
        
        const batchResults = await Promise.all(promises);
        batchResults.forEach(({ url, favicon }) => {
            results[url] = favicon;
        });
        
        // 批次间延迟
        if (i + batchSize < urls.length) {
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    }
    
    return results;
}

// 清理过期缓存
function cleanExpiredCache() {
    const sql = "DELETE FROM favicon_cache WHERE expires_at <= datetime('now')";
    
    cacheDb.run(sql, function(err) {
        if (err) {
            console.error('清理过期缓存失败:', err);
        } else if (this.changes > 0) {
            console.log(`清理了 ${this.changes} 条过期图标缓存`);
        }
    });
}

// 定期清理过期缓存（每天执行一次）
setInterval(cleanExpiredCache, 24 * 60 * 60 * 1000);

module.exports = {
    initFaviconCache,
    getFavicon,
    getFavicons,
    cleanExpiredCache
};
