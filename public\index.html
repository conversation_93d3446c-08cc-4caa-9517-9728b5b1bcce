<!DOCTYPE html>
<html lang="zh-CN" class="">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网址收藏系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <!-- MDI 图标库 -->
    <link rel="stylesheet" href="materialdesignicons.min.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b',
                        success: '#10b981',
                        warning: '#f59e0b',
                        danger: '#ef4444'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 dark:bg-gray-900 min-h-screen transition-colors duration-300">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 顶部导航 -->
        <nav class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-6 transition-colors duration-300">
            <div class="px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <span class="text-2xl">🔖</span>
                        <div class="flex items-center space-x-2">
                            <h1 class="text-xl font-bold text-gray-900 dark:text-white">网址收藏系统</h1>
                            <span class="text-gray-400 dark:text-gray-500">•</span>
                            <p class="text-sm text-gray-500 dark:text-gray-400">管理和组织你的网址收藏</p>
                        </div>
                    </div>

                    <div class="flex items-center space-x-3">
                        <!-- 快照页面入口 -->
                        <a href="/kz" class="inline-flex items-center px-3 py-2 text-sm font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors duration-200"
                           title="进入快照页面">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            📸 快照列表
                        </a>

                        <!-- 自动导出状态 -->
                        <div id="exportStatus" class="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                            <span>自动备份已启用</span>
                        </div>

                        <!-- 主题切换按钮 -->
                        <button id="themeToggle" onclick="toggleTheme()"
                                class="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200"
                                title="切换主题">
                        <!-- 太阳图标 (浅色主题时显示) -->
                        <svg id="sunIcon" class="w-5 h-5 hidden dark:block" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                        </svg>
                        <!-- 月亮图标 (深色主题时显示) -->
                        <svg id="moonIcon" class="w-5 h-5 block dark:hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </nav>

        <!-- 控制区域 -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6 transition-colors duration-300">
            <div class="flex flex-col lg:flex-row gap-4 items-start lg:items-center">
                <!-- 搜索区域 -->
                <div class="flex flex-col sm:flex-row gap-2 flex-1">
                    <div class="relative flex-1">
                        <input type="text" id="searchInput" placeholder="搜索收藏..."
                               class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent outline-none transition-all">
                        <svg class="absolute right-3 top-2.5 h-5 w-5 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <div class="flex gap-2">
                        <button onclick="searchBookmarks()"
                                class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-blue-600 transition-colors">
                            搜索
                        </button>
                        <button onclick="showAllBookmarks()"
                                class="px-4 py-2 bg-gray-500 dark:bg-gray-600 text-white rounded-lg hover:bg-gray-600 dark:hover:bg-gray-500 transition-colors">
                            显示全部
                        </button>
                    </div>
                </div>

                <!-- 筛选区域 -->
                <div class="flex flex-col sm:flex-row gap-2 items-start sm:items-center">
                    <!-- 分类筛选 -->
                    <div class="flex gap-2 items-center">
                        <select id="categoryFilter"
                                class="px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent outline-none">
                            <option value="">所有分类</option>
                        </select>
                        <button onclick="filterByCategory()"
                                class="px-4 py-2 bg-secondary text-white rounded-lg hover:bg-slate-600 dark:hover:bg-slate-500 transition-colors">
                            筛选
                        </button>
                    </div>

                    <!-- 状态筛选 -->
                    <div class="flex gap-2 items-center">
                        <select id="statusFilter"
                                class="px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent outline-none">
                            <option value="">所有状态</option>
                            <option value="默认">默认</option>
                            <option value="未读">未读</option>
                            <option value="已读">已读</option>
                            <option value="收藏">收藏</option>
                            <option value="临时">临时</option>
                        </select>
                        <button onclick="filterByStatus()"
                                class="px-4 py-2 bg-warning text-white rounded-lg hover:bg-yellow-600 transition-colors">
                            状态筛选
                        </button>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="flex gap-2">
                    <button onclick="showExportModal()"
                            class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors font-medium">
                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        导出Excel
                    </button>
                    <button onclick="showBatchAddModal()"
                            class="px-6 py-2 bg-success text-white rounded-lg hover:bg-green-600 transition-colors font-medium">
                        + 批量添加收藏
                    </button>
                </div>
            </div>
        </div>

        <!-- 编辑表单 -->
        <div id="editForm" class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6 transition-colors duration-300" style="display: none;">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">编辑收藏</h3>
                <button type="button" onclick="hideEditForm()" class="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <form id="editFormElement" class="space-y-6">
                <input type="hidden" id="editBookmarkId">

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label for="editTitle" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            标题 <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="editTitle" required
                               class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent outline-none transition-all">
                    </div>

                    <div>
                        <label for="editCategory" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">分类</label>
                        <input type="text" id="editCategory" placeholder="默认"
                               class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent outline-none transition-all">
                    </div>

                    <div>
                        <label for="editStatus" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">状态</label>
                        <select id="editStatus"
                                class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent outline-none transition-all">
                            <option value="默认">默认</option>
                            <option value="未读">未读</option>
                            <option value="已读">已读</option>
                            <option value="收藏">收藏</option>
                            <option value="临时">临时</option>
                        </select>
                    </div>
                </div>

                <div>
                    <label for="editUrl" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        网址 <span class="text-red-500">*</span>
                    </label>
                    <input type="url" id="editUrl" required
                           class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent outline-none transition-all">
                </div>

                <div>
                    <label for="editDescription" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">描述</label>
                    <textarea id="editDescription" rows="3"
                              class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent outline-none transition-all resize-none"></textarea>
                </div>

                <div class="flex gap-3 pt-4">
                    <button type="submit"
                            class="flex-1 sm:flex-none px-6 py-2 bg-primary text-white rounded-lg hover:bg-blue-600 transition-colors font-medium">
                        保存
                    </button>
                    <button type="button" onclick="hideEditForm()"
                            class="flex-1 sm:flex-none px-6 py-2 bg-gray-500 dark:bg-gray-600 text-white rounded-lg hover:bg-gray-600 dark:hover:bg-gray-500 transition-colors font-medium">
                        取消
                    </button>
                </div>
            </form>
        </div>

        <!-- 批量添加弹窗 -->
        <div id="batchAddModal" class="fixed inset-0 bg-black bg-opacity-50 dark:bg-black dark:bg-opacity-70 flex items-center justify-center z-50 transition-colors duration-300" style="display: none;">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden transition-colors duration-300">
                <!-- 弹窗头部 -->
                <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">批量添加收藏</h3>
                    <button type="button" onclick="hideBatchAddModal()" class="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- 弹窗内容 -->
                <div class="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
                    <div class="mb-4">
                        <p class="text-sm text-gray-600 dark:text-gray-300 mb-4">
                            每行一条收藏，格式：<code class="bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-2 py-1 rounded">标题|网址|描述</code>
                            <br>
                            <span class="text-gray-500 dark:text-gray-400">描述可选，用竖线(|)分隔。分类和状态通过下方的下拉框统一设置。示例：</span>
                        </p>
                        <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg text-sm font-mono text-gray-700 dark:text-gray-300 mb-4">
                            Google|https://www.google.com|全球最大的搜索引擎<br>
                            GitHub|https://github.com|代码托管平台<br>
                            百度|https://www.baidu.com|中文搜索引擎<br>
                            临时网站|https://example.com|
                        </div>
                    </div>

                    <form id="batchAddForm">
                        <div class="mb-4">
                            <div class="flex items-center justify-between mb-2">
                                <label for="batchInput" class="text-sm font-medium text-gray-700 dark:text-gray-300">
                                    批量输入 <span class="text-red-500">*</span>
                                </label>
                                <button type="button" onclick="pasteFromClipboard()"
                                        class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors duration-200"
                                        title="从剪切板粘贴内容">
                                    <svg class="w-3.5 h-3.5 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                    </svg>
                                    从剪切板粘贴
                                </button>
                            </div>
                            <textarea id="batchInput" rows="15" required
                                      placeholder="请输入收藏信息，每行一条..."
                                      class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent outline-none transition-all resize-none font-mono text-sm"></textarea>
                        </div>

                        <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg mb-4">
                            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">统一设置（应用于所有添加的收藏）</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="batchCategory" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">分类</label>
                                    <div class="flex gap-2">
                                        <select id="batchCategorySelect" onchange="updateBatchCategory()"
                                                class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent outline-none transition-all">
                                            <option value="">选择分类</option>
                                            <option value="默认">默认</option>
                                        </select>
                                        <span class="text-gray-500 dark:text-gray-400 self-center">或</span>
                                        <input type="text" id="batchCategory" placeholder="自定义分类"
                                               class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent outline-none transition-all">
                                    </div>
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">所有收藏将使用此分类</p>
                                </div>

                                <div>
                                    <label for="batchStatus" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">状态</label>
                                    <select id="batchStatus"
                                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent outline-none transition-all">
                                        <option value="默认">默认</option>
                                        <option value="未读" selected>未读</option>
                                        <option value="已读">已读</option>
                                        <option value="收藏">收藏</option>
                                        <option value="临时">临时</option>
                                    </select>
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">所有收藏将使用此状态</p>
                                </div>
                            </div>
                        </div>

                        <div class="flex gap-3 pt-4">
                            <button type="submit"
                                    class="flex-1 sm:flex-none px-6 py-2 bg-success text-white rounded-lg hover:bg-green-600 transition-colors font-medium">
                                批量添加
                            </button>
                            <button type="button" onclick="hideBatchAddModal()"
                                    class="flex-1 sm:flex-none px-6 py-2 bg-gray-500 dark:bg-gray-600 text-white rounded-lg hover:bg-gray-600 dark:hover:bg-gray-500 transition-colors font-medium">
                                取消
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 导出Excel弹窗 -->
        <div id="exportModal" class="fixed inset-0 bg-black bg-opacity-50 dark:bg-black dark:bg-opacity-70 flex items-center justify-center z-50 transition-colors duration-300" style="display: none;">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4 transition-colors duration-300">
                <!-- 弹窗头部 -->
                <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">导出Excel</h3>
                    <button type="button" onclick="hideExportModal()" class="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- 弹窗内容 -->
                <div class="p-6">
                    <form id="exportForm">
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">导出范围</label>
                                <div class="space-y-2">
                                    <label class="flex items-center">
                                        <input type="radio" name="exportType" value="all" checked
                                               class="text-primary focus:ring-primary border-gray-300 dark:border-gray-600">
                                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">导出全部收藏</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" name="exportType" value="filtered"
                                               class="text-primary focus:ring-primary border-gray-300 dark:border-gray-600">
                                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">按条件筛选导出</span>
                                    </label>
                                </div>
                            </div>

                            <div id="filterOptions" class="space-y-4" style="display: none;">
                                <div>
                                    <label for="exportCategory" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">分类筛选</label>
                                    <select id="exportCategory"
                                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent outline-none">
                                        <option value="">所有分类</option>
                                    </select>
                                </div>

                                <div>
                                    <label for="exportStatus" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">状态筛选</label>
                                    <select id="exportStatus"
                                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent outline-none">
                                        <option value="">所有状态</option>
                                        <option value="默认">默认</option>
                                        <option value="未读">未读</option>
                                        <option value="已读">已读</option>
                                        <option value="收藏">收藏</option>
                                        <option value="临时">临时</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="flex gap-3 pt-6">
                            <button type="submit"
                                    class="flex-1 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors font-medium">
                                <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                开始导出
                            </button>
                            <button type="button" onclick="hideExportModal()"
                                    class="flex-1 px-4 py-2 bg-gray-500 dark:bg-gray-600 text-white rounded-lg hover:bg-gray-600 dark:hover:bg-gray-500 transition-colors font-medium">
                                取消
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 收藏列表 -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden transition-colors duration-300">
            <!-- 列表头部 -->
            <div class="bg-gray-50 dark:bg-gray-700 px-6 py-3 border-b border-gray-200 dark:border-gray-600">
                <div class="flex items-center justify-between">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white">收藏列表</h2>
                    <div class="text-sm text-gray-500 dark:text-gray-400" id="bookmarkCount">
                        <!-- 收藏数量将通过 JavaScript 更新 -->
                    </div>
                </div>
            </div>

            <!-- 列表内容 -->
            <div id="bookmarksList">
                <!-- 收藏项将通过 JavaScript 动态加载 -->
            </div>

            <!-- 分页控件 -->
            <div id="paginationContainer" class="px-6 py-4 border-t border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700" style="display: none;">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-4">
                        <div class="text-sm text-gray-700 dark:text-gray-300">
                            显示第 <span id="pageInfo"></span> 条记录，共 <span id="totalInfo"></span> 条
                        </div>
                        <div class="flex items-center gap-2">
                            <label for="pageSizeSelect" class="text-sm text-gray-700 dark:text-gray-300">每页显示:</label>
                            <select id="pageSizeSelect" onchange="changePageSize()"
                                    class="px-2 py-1 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded text-sm">
                                <option value="10">10</option>
                                <option value="20" selected>20</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                        </div>
                    </div>

                    <div class="flex items-center gap-2">
                        <button id="prevPageBtn" onclick="goToPage(currentPage - 1)"
                                class="px-3 py-1 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                disabled>
                            上一页
                        </button>

                        <div id="pageNumbers" class="flex items-center gap-1">
                            <!-- 页码按钮将通过 JavaScript 动态生成 -->
                        </div>

                        <button id="nextPageBtn" onclick="goToPage(currentPage + 1)"
                                class="px-3 py-1 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                disabled>
                            下一页
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
