const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// 创建快照数据库连接
const dbPath = path.join(__dirname, 'snapshots.db');
const db = new sqlite3.Database(dbPath);

// 初始化快照数据库表
function initSnapshotDatabase() {
    return new Promise((resolve, reject) => {
        // 创建快照收藏表
        db.run(`CREATE TABLE IF NOT EXISTS snapshot_bookmarks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            url TEXT NOT NULL,
            description TEXT,
            category TEXT DEFAULT '默认',
            status TEXT DEFAULT '默认',
            snapshot_url TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )`, (err) => {
            if (err) {
                console.error('创建快照收藏表失败:', err);
                reject(err);
                return;
            }

            // 创建快照分类表
            db.run(`CREATE TABLE IF NOT EXISTS snapshot_categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                category TEXT UNIQUE NOT NULL
            )`, (err) => {
                if (err) {
                    console.error('创建快照分类表失败:', err);
                    reject(err);
                    return;
                }

                // 创建快照状态表
                db.run(`CREATE TABLE IF NOT EXISTS snapshot_statuses (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    status TEXT UNIQUE NOT NULL
                )`, (err) => {
                    if (err) {
                        console.error('创建快照状态表失败:', err);
                        reject(err);
                        return;
                    }

                    // 插入默认分类和状态
                    insertDefaultSnapshotData().then(() => {
                        console.log('快照数据库表初始化成功');
                        resolve();
                    }).catch(reject);
                });
            });
        });
    });
}

// 插入默认快照数据
function insertDefaultSnapshotData() {
    return new Promise((resolve, reject) => {
        const defaultCategories = ['默认', '工作', '学习', '娱乐', '工具', '新闻', '购物', '社交'];
        const defaultStatuses = ['默认', '未读', '已读', '收藏', '临时'];

        let completed = 0;
        const total = defaultCategories.length + defaultStatuses.length;

        // 插入默认分类
        defaultCategories.forEach(category => {
            db.run('INSERT OR IGNORE INTO snapshot_categories (category) VALUES (?)', [category], (err) => {
                if (err) console.error('插入快照分类失败:', err);
                completed++;
                if (completed === total) resolve();
            });
        });

        // 插入默认状态
        defaultStatuses.forEach(status => {
            db.run('INSERT OR IGNORE INTO snapshot_statuses (status) VALUES (?)', [status], (err) => {
                if (err) console.error('插入快照状态失败:', err);
                completed++;
                if (completed === total) resolve();
            });
        });
    });
}

// 快照收藏操作函数
const snapshotBookmarkOperations = {
    // 获取所有快照收藏
    getAll: (callback) => {
        db.all('SELECT * FROM snapshot_bookmarks ORDER BY created_at DESC', callback);
    },

    // 根据ID获取快照收藏
    getById: (id, callback) => {
        db.get('SELECT * FROM snapshot_bookmarks WHERE id = ?', [id], callback);
    },

    // 检查URL是否已存在（在收藏和快照中）
    checkUrlExists: (url, callback) => {
        // 检查快照收藏中是否存在
        const checkSnapshotsSql = 'SELECT COUNT(*) as count FROM snapshot_bookmarks WHERE url = ?';
        db.get(checkSnapshotsSql, [url], (err, snapshotResult) => {
            if (err) {
                callback(err, null);
                return;
            }

            if (snapshotResult.count > 0) {
                callback(null, { exists: true, type: 'snapshot' });
                return;
            }

            // 检查普通收藏中是否存在
            try {
                const mainDb = require('./database');
                const checkBookmarksSql = 'SELECT COUNT(*) as count FROM bookmarks WHERE url = ?';
                mainDb.db.get(checkBookmarksSql, [url], (err, bookmarkResult) => {
                if (err) {
                    callback(err, null);
                    return;
                }

                    if (bookmarkResult.count > 0) {
                        callback(null, { exists: true, type: 'bookmark' });
                        return;
                    }

                    callback(null, { exists: false });
                });
            } catch (e) {
                // 如果无法访问主数据库，只检查快照数据库
                callback(null, { exists: false });
            }
        });
    },

    // 添加快照收藏
    add: (bookmark, callback) => {
        const { title, url, description, category, status, snapshot_url } = bookmark;

        // 先检查URL是否已存在
        snapshotBookmarkOperations.checkUrlExists(url, (err, result) => {
            if (err) {
                callback(err, null);
                return;
            }

            if (result.exists) {
                const errorMsg = result.type === 'snapshot' ?
                    '该网址已存在于快照收藏中' :
                    '该网址已存在于收藏夹中';
                callback(new Error(errorMsg), null);
                return;
            }

            // URL不存在，可以添加
            db.run(
                'INSERT INTO snapshot_bookmarks (title, url, description, category, status, snapshot_url) VALUES (?, ?, ?, ?, ?, ?)',
                [title, url, description || '', category || '默认', status || '默认', snapshot_url || ''],
                function(err) {
                    callback(err, this ? this.lastID : null);
                }
            );
        });
    },

    // 更新快照收藏
    update: (id, bookmark, callback) => {
        const { title, url, description, category, status, snapshot_url } = bookmark;
        db.run(
            'UPDATE snapshot_bookmarks SET title = ?, url = ?, description = ?, category = ?, status = ?, snapshot_url = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [title, url, description, category, status, snapshot_url, id],
            callback
        );
    },

    // 删除快照收藏
    delete: (id, callback) => {
        db.run('DELETE FROM snapshot_bookmarks WHERE id = ?', [id], callback);
    },

    // 批量添加快照收藏
    addBatch: (bookmarks, callback) => {
        let processedCount = 0;
        let skippedCount = 0;
        const errors = [];

        // 逐个检查并添加
        const processBookmark = (index) => {
            if (index >= bookmarks.length) {
                // 所有书签处理完成
                if (errors.length > 0) {
                    callback(new Error(`批量添加完成，成功: ${processedCount}，跳过重复: ${skippedCount}，错误: ${errors.length}`));
                } else {
                    callback(null, { processed: processedCount, skipped: skippedCount });
                }
                return;
            }

            const bookmark = bookmarks[index];
            const { title, url, description, category, status, snapshot_url } = bookmark;

            // 检查URL是否已存在
            snapshotBookmarkOperations.checkUrlExists(url, (err, result) => {
                if (err) {
                    errors.push(`${url}: ${err.message}`);
                    processBookmark(index + 1);
                    return;
                }

                if (result.exists) {
                    skippedCount++;
                    processBookmark(index + 1);
                    return;
                }

                // URL不存在，可以添加
                db.run(
                    'INSERT INTO snapshot_bookmarks (title, url, description, category, status, snapshot_url) VALUES (?, ?, ?, ?, ?, ?)',
                    [title, url, description || '', category || '默认', status || '默认', snapshot_url || ''],
                    function(err) {
                        if (err) {
                            errors.push(`${url}: ${err.message}`);
                        } else {
                            processedCount++;
                        }
                        processBookmark(index + 1);
                    }
                );
            });
        };

        processBookmark(0);
    }
};

// 快照分类操作函数
const snapshotCategoryOperations = {
    getAll: (callback) => {
        db.all('SELECT DISTINCT category FROM snapshot_categories ORDER BY category', callback);
    },

    add: (category, callback) => {
        db.run('INSERT OR IGNORE INTO snapshot_categories (category) VALUES (?)', [category], callback);
    }
};

// 快照状态操作函数
const snapshotStatusOperations = {
    getAll: (callback) => {
        db.all('SELECT DISTINCT status FROM snapshot_statuses ORDER BY status', callback);
    }
};

module.exports = {
    initSnapshotDatabase,
    snapshotBookmarkOperations,
    snapshotCategoryOperations,
    snapshotStatusOperations,
    db: db
};
