<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快照调试页面</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-center mb-8">快照功能调试</h1>
        
        <!-- 快照URL生成测试 -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4">快照URL生成测试</h2>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">输入网址：</label>
                    <input type="url" id="testUrl" value="https://www.google.com" 
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                </div>
                <button onclick="generateTestSnapshot()" 
                        class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                    生成快照URL
                </button>
                <div id="generatedUrls" class="space-y-2"></div>
            </div>
        </div>

        <!-- 快照加载测试 -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4">快照加载测试</h2>
            <div id="snapshotTests" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <!-- 测试结果将在这里显示 -->
            </div>
        </div>

        <!-- API测试 -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4">API测试</h2>
            <div class="space-y-4">
                <button onclick="testGetSnapshots()" 
                        class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 mr-2">
                    测试获取快照列表
                </button>
                <button onclick="testAddSnapshot()" 
                        class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 mr-2">
                    测试添加快照
                </button>
                <button onclick="clearAllSnapshots()" 
                        class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700">
                    清空所有快照
                </button>
                <div id="apiResults" class="mt-4 p-4 bg-gray-100 rounded-lg">
                    <pre id="apiOutput" class="text-sm"></pre>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 快照服务配置
        const SNAPSHOT_SERVICES = [
            {
                name: 'Thum.io',
                url: (targetUrl) => `https://image.thum.io/get/width/1200/crop/800/${encodeURIComponent(targetUrl)}`
            },
            {
                name: 'Screenshot Machine',
                url: (targetUrl) => `https://api.screenshotmachine.com/?key=demo&url=${encodeURIComponent(targetUrl)}&dimension=1200x800`
            },
            {
                name: 'Placeholder.com',
                url: (targetUrl) => `https://via.placeholder.com/1200x800/cccccc/666666?text=${encodeURIComponent(extractDomain(targetUrl) || 'Website')}`
            }
        ];

        function extractDomain(url) {
            try {
                const urlObj = new URL(url);
                return urlObj.hostname;
            } catch (error) {
                return null;
            }
        }

        function generateTestSnapshot() {
            const url = document.getElementById('testUrl').value.trim();
            if (!url) {
                alert('请输入网址');
                return;
            }

            const container = document.getElementById('generatedUrls');
            container.innerHTML = '';

            SNAPSHOT_SERVICES.forEach((service, index) => {
                const snapshotUrl = service.url(url);
                const div = document.createElement('div');
                div.className = 'p-3 border border-gray-200 rounded-lg';
                div.innerHTML = `
                    <h3 class="font-medium text-gray-900 mb-2">${service.name}</h3>
                    <p class="text-xs text-gray-600 break-all mb-2">${snapshotUrl}</p>
                    <button onclick="testSnapshotLoad('${snapshotUrl}', '${service.name}')" 
                            class="px-3 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600">
                        测试加载
                    </button>
                `;
                container.appendChild(div);
            });
        }

        function testSnapshotLoad(snapshotUrl, serviceName) {
            const container = document.getElementById('snapshotTests');
            
            const testDiv = document.createElement('div');
            testDiv.className = 'border border-gray-200 rounded-lg p-4';
            testDiv.innerHTML = `
                <h3 class="font-medium text-gray-900 mb-2">${serviceName}</h3>
                <div class="w-full h-32 bg-gray-100 rounded flex items-center justify-center">
                    <div class="animate-pulse">📸 加载中...</div>
                </div>
                <p class="text-xs text-gray-500 mt-2 break-all">${snapshotUrl}</p>
            `;
            
            container.appendChild(testDiv);

            const img = document.createElement('img');
            img.src = snapshotUrl;
            img.className = 'w-full h-32 object-cover rounded';
            img.style.opacity = '0';
            img.style.transition = 'opacity 0.3s ease';

            img.onload = function() {
                const placeholder = testDiv.querySelector('.bg-gray-100');
                placeholder.innerHTML = '';
                placeholder.appendChild(img);
                img.style.opacity = '1';
                
                // 添加成功标识
                const badge = document.createElement('div');
                badge.className = 'absolute top-2 right-2 bg-green-500 text-white text-xs px-2 py-1 rounded';
                badge.textContent = '✓ 成功';
                placeholder.style.position = 'relative';
                placeholder.appendChild(badge);
            };

            img.onerror = function() {
                const placeholder = testDiv.querySelector('.bg-gray-100');
                placeholder.innerHTML = `
                    <div class="text-red-500 text-center">
                        <div class="text-2xl">❌</div>
                        <div class="text-xs mt-1">加载失败</div>
                    </div>
                `;
            };

            // 10秒超时
            setTimeout(() => {
                if (img.style.opacity === '0') {
                    const placeholder = testDiv.querySelector('.bg-gray-100');
                    placeholder.innerHTML = `
                        <div class="text-yellow-600 text-center">
                            <div class="text-2xl">⏱️</div>
                            <div class="text-xs mt-1">超时</div>
                        </div>
                    `;
                }
            }, 10000);
        }

        async function testGetSnapshots() {
            try {
                const response = await fetch('/kz/api/snapshots');
                const data = await response.json();
                document.getElementById('apiOutput').textContent = 
                    `获取快照列表成功:\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                document.getElementById('apiOutput').textContent = 
                    `获取快照列表失败:\n${error.message}`;
            }
        }

        async function testAddSnapshot() {
            const testSnapshot = {
                title: '测试快照',
                url: 'https://www.google.com',
                description: '这是一个测试快照',
                category: '测试',
                status: '默认',
                snapshot_url: 'https://image.thum.io/get/width/1200/crop/800/https://www.google.com'
            };

            try {
                const response = await fetch('/kz/api/snapshots', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testSnapshot)
                });
                const data = await response.json();
                document.getElementById('apiOutput').textContent = 
                    `添加快照成功:\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                document.getElementById('apiOutput').textContent = 
                    `添加快照失败:\n${error.message}`;
            }
        }

        async function clearAllSnapshots() {
            if (!confirm('确定要清空所有快照吗？此操作不可恢复！')) {
                return;
            }

            try {
                // 先获取所有快照
                const response = await fetch('/kz/api/snapshots');
                const snapshots = await response.json();
                
                // 逐个删除
                for (const snapshot of snapshots) {
                    await fetch(`/kz/api/snapshots/${snapshot.id}`, {
                        method: 'DELETE'
                    });
                }
                
                document.getElementById('apiOutput').textContent = 
                    `成功清空 ${snapshots.length} 个快照`;
            } catch (error) {
                document.getElementById('apiOutput').textContent = 
                    `清空快照失败:\n${error.message}`;
            }
        }

        // 页面加载时自动生成测试
        window.onload = function() {
            generateTestSnapshot();
        };
    </script>
</body>
</html>
