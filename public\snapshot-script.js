// 全局变量
let snapshots = [];
let categories = [];
let statuses = [];
let currentPage = 1;
let itemsPerPage = 12;
let filteredSnapshots = [];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('快照页面开始初始化...');

    initializeTheme();
    loadCategories();
    loadStatuses();
    loadSnapshots();

    // 绑定事件监听器
    bindEventListeners();

    console.log('快照页面初始化完成');
});

// 绑定事件监听器
function bindEventListeners() {
    // 主题切换
    document.getElementById('themeToggle').addEventListener('click', toggleTheme);

    // 搜索和筛选
    document.getElementById('searchInput').addEventListener('input', debounce(filterSnapshots, 300));
    document.getElementById('categoryFilter').addEventListener('change', filterSnapshots);
    document.getElementById('statusFilter').addEventListener('change', filterSnapshots);

    // 表单提交
    document.getElementById('addForm').addEventListener('submit', handleAddSubmit);
    document.getElementById('batchAddForm').addEventListener('submit', handleBatchAddSubmit);

    // URL输入框失焦时提示生成快照
    document.getElementById('addUrl').addEventListener('blur', function() {
        const snapshotUrlInput = document.getElementById('addSnapshotUrl');
        if (this.value.trim() && !snapshotUrlInput.value.trim()) {
            // 显示提示信息
            const button = document.querySelector('[onclick="autoFillSnapshotUrl()"]');
            if (button) {
                button.classList.add('animate-pulse');
                setTimeout(() => {
                    button.classList.remove('animate-pulse');
                }, 2000);
            }
        }
    });

    // 键盘快捷键
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            hideAddModal();
            hideBatchAddModal();
        }
    });
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 主题相关函数
function initializeTheme() {
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark' || (!savedTheme && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
        document.documentElement.classList.add('dark');
    }
}

function toggleTheme() {
    document.documentElement.classList.toggle('dark');
    const isDark = document.documentElement.classList.contains('dark');
    localStorage.setItem('theme', isDark ? 'dark' : 'light');
}

// 加载分类
async function loadCategories() {
    try {
        const response = await fetch('/kz/api/categories');
        categories = await response.json();
        
        // 更新分类筛选器
        const categoryFilter = document.getElementById('categoryFilter');
        categoryFilter.innerHTML = '<option value="">全部分类</option>';
        categories.forEach(category => {
            categoryFilter.innerHTML += `<option value="${category}">${category}</option>`;
        });
        
        // 更新添加表单的分类选择器
        updateCategorySelectors();
    } catch (error) {
        console.error('加载分类失败:', error);
    }
}

// 加载状态
async function loadStatuses() {
    try {
        const response = await fetch('/kz/api/statuses');
        statuses = await response.json();
        
        // 更新状态筛选器
        const statusFilter = document.getElementById('statusFilter');
        statusFilter.innerHTML = '<option value="">全部状态</option>';
        statuses.forEach(status => {
            statusFilter.innerHTML += `<option value="${status}">${status}</option>`;
        });
        
        // 更新添加表单的状态选择器
        updateStatusSelectors();
    } catch (error) {
        console.error('加载状态失败:', error);
    }
}

// 更新分类选择器
function updateCategorySelectors() {
    const selectors = ['addCategory', 'batchCategory'];
    selectors.forEach(id => {
        const selector = document.getElementById(id);
        if (selector) {
            selector.innerHTML = '';
            categories.forEach(category => {
                const selected = category === '默认' ? 'selected' : '';
                selector.innerHTML += `<option value="${category}" ${selected}>${category}</option>`;
            });
        }
    });
}

// 更新状态选择器
function updateStatusSelectors() {
    const selectors = ['addStatus', 'batchStatus'];
    selectors.forEach(id => {
        const selector = document.getElementById(id);
        if (selector) {
            selector.innerHTML = '';
            statuses.forEach(status => {
                const selected = status === '默认' ? 'selected' : '';
                selector.innerHTML += `<option value="${status}" ${selected}>${status}</option>`;
            });
        }
    });
}

// 加载快照列表
async function loadSnapshots() {
    try {
        const response = await fetch('/kz/api/snapshots');
        const data = await response.json();

        // 确保每个快照都有有效的ID
        snapshots = data.map(snapshot => {
            if (!snapshot.id) {
                console.warn('快照缺少ID:', snapshot);
            }
            return {
                ...snapshot,
                id: snapshot.id || Date.now() + Math.random() // 临时ID，如果缺少的话
            };
        });

        console.log('加载快照数据:', snapshots);
        filterSnapshots();
    } catch (error) {
        console.error('加载快照失败:', error);
        showToast('加载快照失败', 'error');
    }
}

// 筛选快照
function filterSnapshots() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const categoryFilter = document.getElementById('categoryFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;
    
    filteredSnapshots = snapshots.filter(snapshot => {
        const matchesSearch = !searchTerm || 
            snapshot.title.toLowerCase().includes(searchTerm) ||
            snapshot.url.toLowerCase().includes(searchTerm) ||
            (snapshot.description && snapshot.description.toLowerCase().includes(searchTerm));
        
        const matchesCategory = !categoryFilter || snapshot.category === categoryFilter;
        const matchesStatus = !statusFilter || snapshot.status === statusFilter;
        
        return matchesSearch && matchesCategory && matchesStatus;
    });
    
    currentPage = 1;
    renderSnapshots();
    renderPagination();
}

// 渲染快照卡片
function renderSnapshots() {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const snapshotsToRender = filteredSnapshots.slice(startIndex, endIndex);

    const grid = document.getElementById('snapshotGrid');

    if (snapshotsToRender.length === 0) {
        grid.innerHTML = `
            <div class="col-span-full text-center py-12">
                <div class="text-gray-400 dark:text-gray-500 text-6xl mb-4">📸</div>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">暂无快照收藏</h3>
                <p class="text-gray-500 dark:text-gray-400">开始添加你的第一个快照收藏吧！</p>
            </div>
        `;
        return;
    }

    grid.innerHTML = snapshotsToRender.map(snapshot => createSnapshotCard(snapshot)).join('');

    // 验证按钮是否正确渲染
    setTimeout(() => {
        const editButtons = document.querySelectorAll('[data-snapshot-id]');
        console.log(`渲染了 ${snapshotsToRender.length} 个快照卡片，找到 ${editButtons.length} 个操作按钮`);

        if (editButtons.length === 0 && snapshotsToRender.length > 0) {
            console.error('警告：快照卡片已渲染但未找到操作按钮！');
        }
    }, 100);
}

// 创建快照卡片
function createSnapshotCard(snapshot) {
    // 确保快照有有效的ID
    if (!snapshot.id) {
        console.error('快照缺少ID:', snapshot);
        return '';
    }

    console.log('创建快照卡片 ID:', snapshot.id, '标题:', snapshot.title);

    // 简化的快照预览
    const hasSnapshotUrl = snapshot.snapshot_url && snapshot.snapshot_url.trim();
    const snapshotPreview = hasSnapshotUrl
        ? `<img src="${snapshot.snapshot_url}" alt="${snapshot.title}" class="w-full h-48 object-cover bg-gray-200" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuaXoOW/q+eFpzwvdGV4dD48L3N2Zz4='">`
        : `<div class="w-full h-48 bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-4xl">📸</div>`;

    // 简化的图标
    const iconHtml = `<div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center text-blue-600 dark:text-blue-400 font-bold border border-blue-200 dark:border-blue-800">${snapshot.title.charAt(0).toUpperCase()}</div>`;

    return `
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-xl hover:-translate-y-2 transition-all duration-300 ease-out">
            <!-- 快照预览 -->
            <div class="relative">
                ${snapshotPreview}
                <div class="absolute top-2 right-2 flex flex-col gap-1">
                    <!-- 状态标签 -->
                    <span class="px-2 py-1 rounded-full text-xs font-medium ${getStatusBadgeClass(snapshot.status || '默认')} border shadow-sm">
                        ${snapshot.status || '默认'}
                    </span>
                    <!-- 分类标签 -->
                    <span class="px-2 py-1 rounded-full text-xs font-medium ${getCategoryBadgeClass(snapshot.category || '默认')} border shadow-sm">
                        ${snapshot.category || '默认'}
                    </span>
                </div>
                <!-- 操作按钮覆盖层 -->
                <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-3">
                    <div class="flex items-center justify-between gap-2">
                        <div class="flex items-center gap-2">
                            <a href="${snapshot.url}" target="_blank" class="w-8 h-8 flex items-center justify-center bg-blue-500 hover:bg-blue-600 text-white rounded-full transition-all duration-200 shadow-sm hover:shadow-md hover:scale-110" title="访问网站">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                </svg>
                            </a>
                            ${hasSnapshotUrl ? `
                                <a href="${snapshot.snapshot_url}" target="_blank" class="w-8 h-8 flex items-center justify-center bg-green-500 hover:bg-green-600 text-white rounded-full transition-all duration-200 shadow-sm hover:shadow-md hover:scale-110" title="查看快照">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                </a>
                            ` : ''}
                        </div>
                        <div class="flex items-center gap-1">
                            <button onclick="editSnapshot(${snapshot.id})" class="w-8 h-8 flex items-center justify-center bg-gray-500 hover:bg-gray-600 text-white rounded-full transition-all duration-200 shadow-sm hover:shadow-md hover:scale-110" title="编辑快照" data-snapshot-id="${snapshot.id}">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                            </button>
                            <button onclick="deleteSnapshot(${snapshot.id})" class="w-8 h-8 flex items-center justify-center bg-red-500 hover:bg-red-600 text-white rounded-full transition-all duration-200 shadow-sm hover:shadow-md hover:scale-110" title="删除快照" data-snapshot-id="${snapshot.id}">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 卡片内容 -->
            <div class="p-4">
                <!-- 标题和图标 -->
                <div class="flex items-start gap-3">
                    ${iconHtml}
                    <div class="flex-1 min-w-0">
                        <h3 class="text-sm font-medium text-gray-900 dark:text-white truncate" title="${snapshot.title}">
                            ${snapshot.title}
                        </h3>
                        ${snapshot.description ? `
                            <p class="text-xs text-gray-500 dark:text-gray-400 line-clamp-2 mt-1" title="${snapshot.description}">
                                ${snapshot.description}
                            </p>
                        ` : `
                            <p class="text-xs text-gray-400 dark:text-gray-500 italic mt-1">
                                暂无描述
                            </p>
                        `}
                    </div>
                </div>
            </div>
        </div>
    `;
}

// 获取状态徽章样式
function getStatusBadgeClass(status) {
    const statusClasses = {
        '默认': 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-100 border-gray-200 dark:border-gray-500',
        '未读': 'bg-yellow-100 dark:bg-yellow-600 text-yellow-800 dark:text-yellow-100 border-yellow-200 dark:border-yellow-500',
        '已读': 'bg-green-100 dark:bg-green-600 text-green-800 dark:text-green-100 border-green-200 dark:border-green-500',
        '收藏': 'bg-red-100 dark:bg-red-600 text-red-800 dark:text-red-100 border-red-200 dark:border-red-500',
        '临时': 'bg-purple-100 dark:bg-purple-600 text-purple-800 dark:text-purple-100 border-purple-200 dark:border-purple-500'
    };
    return statusClasses[status] || statusClasses['默认'];
}

// 获取分类徽章样式
function getCategoryBadgeClass(category) {
    const categoryClasses = {
        '默认': 'bg-blue-100 dark:bg-blue-600 text-blue-800 dark:text-blue-100 border-blue-200 dark:border-blue-500',
        '工作': 'bg-indigo-100 dark:bg-indigo-600 text-indigo-800 dark:text-indigo-100 border-indigo-200 dark:border-indigo-500',
        '学习': 'bg-emerald-100 dark:bg-emerald-600 text-emerald-800 dark:text-emerald-100 border-emerald-200 dark:border-emerald-500',
        '娱乐': 'bg-pink-100 dark:bg-pink-600 text-pink-800 dark:text-pink-100 border-pink-200 dark:border-pink-500',
        '工具': 'bg-cyan-100 dark:bg-cyan-600 text-cyan-800 dark:text-cyan-100 border-cyan-200 dark:border-cyan-500',
        '新闻': 'bg-orange-100 dark:bg-orange-600 text-orange-800 dark:text-orange-100 border-orange-200 dark:border-orange-500',
        '购物': 'bg-rose-100 dark:bg-rose-600 text-rose-800 dark:text-rose-100 border-rose-200 dark:border-rose-500',
        '社交': 'bg-violet-100 dark:bg-violet-600 text-violet-800 dark:text-violet-100 border-violet-200 dark:border-violet-500',
        '技术': 'bg-slate-100 dark:bg-slate-600 text-slate-800 dark:text-slate-100 border-slate-200 dark:border-slate-500',
        '设计': 'bg-fuchsia-100 dark:bg-fuchsia-600 text-fuchsia-800 dark:text-fuchsia-100 border-fuchsia-200 dark:border-fuchsia-500'
    };
    return categoryClasses[category] || categoryClasses['默认'];
}

// 渲染分页
function renderPagination() {
    const totalPages = Math.ceil(filteredSnapshots.length / itemsPerPage);
    const pagination = document.getElementById('pagination');

    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }

    let paginationHTML = '<div class="flex items-center space-x-2">';

    // 上一页
    if (currentPage > 1) {
        paginationHTML += `
            <button onclick="changePage(${currentPage - 1})"
                    class="px-3 py-2 text-sm font-medium text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                上一页
            </button>
        `;
    }

    // 页码
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    if (startPage > 1) {
        paginationHTML += `
            <button onclick="changePage(1)"
                    class="px-3 py-2 text-sm font-medium text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                1
            </button>
        `;
        if (startPage > 2) {
            paginationHTML += '<span class="px-2 text-gray-500 dark:text-gray-400">...</span>';
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        const isActive = i === currentPage;
        paginationHTML += `
            <button onclick="changePage(${i})"
                    class="px-3 py-2 text-sm font-medium ${isActive
                        ? 'text-white bg-primary border-primary'
                        : 'text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700'
                    } border rounded-lg transition-colors">
                ${i}
            </button>
        `;
    }

    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            paginationHTML += '<span class="px-2 text-gray-500 dark:text-gray-400">...</span>';
        }
        paginationHTML += `
            <button onclick="changePage(${totalPages})"
                    class="px-3 py-2 text-sm font-medium text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                ${totalPages}
            </button>
        `;
    }

    // 下一页
    if (currentPage < totalPages) {
        paginationHTML += `
            <button onclick="changePage(${currentPage + 1})"
                    class="px-3 py-2 text-sm font-medium text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                下一页
            </button>
        `;
    }

    paginationHTML += '</div>';
    pagination.innerHTML = paginationHTML;
}

// 切换页面
function changePage(page) {
    currentPage = page;
    renderSnapshots();
    renderPagination();
    window.scrollTo({ top: 0, behavior: 'smooth' });
}

// 模态框相关函数
function showAddModal() {
    document.getElementById('addModal').style.display = 'flex';
    document.getElementById('addTitle').focus();
}

function hideAddModal() {
    document.getElementById('addModal').style.display = 'none';
    document.getElementById('addForm').reset();

    // 重置表单提交行为
    document.getElementById('addForm').onsubmit = handleAddSubmit;

    // 重置模态框标题
    const modalTitle = document.querySelector('#addModal h3');
    if (modalTitle) {
        modalTitle.textContent = '添加快照收藏';
    }
}

function showBatchAddModal() {
    document.getElementById('batchAddModal').style.display = 'flex';
    document.getElementById('batchInput').focus();
}

function hideBatchAddModal() {
    document.getElementById('batchAddModal').style.display = 'none';
    document.getElementById('batchAddForm').reset();
}

// 快照服务配置
const SNAPSHOT_SERVICES = [
    {
        name: 'WordPress.com mShots',
        url: (targetUrl) => `https://s0.wp.com/mshots/v1/${targetUrl}?w=1200&h=800`,
        primary: true
    }
];

// 自动生成快照URL
function generateSnapshotUrl(url, serviceIndex = 0) {
    if (!url) return '';

    // 确保URL有协议
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
        url = 'https://' + url;
    }

    // 使用指定的快照服务
    const service = SNAPSHOT_SERVICES[serviceIndex] || SNAPSHOT_SERVICES[0];
    return service.url(url);
}

// 获取所有可用的快照URL
function getAllSnapshotUrls(url) {
    if (!url) return [];

    // 确保URL有协议
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
        url = 'https://' + url;
    }

    return SNAPSHOT_SERVICES.map((service, index) => ({
        name: service.name,
        url: service.url(url),
        index: index,
        primary: service.primary
    }));
}

// 自动填充快照URL
function autoFillSnapshotUrl() {
    const urlInput = document.getElementById('addUrl');
    const snapshotUrlInput = document.getElementById('addSnapshotUrl');

    if (urlInput.value.trim()) {
        const snapshotUrl = generateSnapshotUrl(urlInput.value.trim());
        snapshotUrlInput.value = snapshotUrl;
        showToast('已自动生成快照地址（WordPress mShots服务）', 'info', 3000);

        // 添加服务选择和预览按钮
        addSnapshotControls(urlInput.value.trim());
    } else {
        showToast('请先填写网址', 'warning');
    }
}

// 添加快照控制按钮
function addSnapshotControls(url) {
    const container = document.getElementById('addSnapshotUrl').parentElement;

    // 移除已存在的控制按钮
    const existingControls = container.querySelector('.snapshot-controls');
    if (existingControls) {
        existingControls.remove();
    }

    // 创建控制按钮容器
    const controlsDiv = document.createElement('div');
    controlsDiv.className = 'snapshot-controls mt-2 flex gap-2 flex-wrap';

    // 预览按钮
    const previewBtn = document.createElement('button');
    previewBtn.type = 'button';
    previewBtn.className = 'inline-flex items-center px-3 py-1.5 text-xs font-medium text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors duration-200';
    previewBtn.innerHTML = '<i class="mdi mdi-eye mr-1.5"></i>预览快照';
    previewBtn.onclick = () => previewSnapshot(document.getElementById('addSnapshotUrl').value);

    // 服务切换按钮
    const serviceBtn = document.createElement('button');
    serviceBtn.type = 'button';
    serviceBtn.className = 'inline-flex items-center px-3 py-1.5 text-xs font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors duration-200';
    serviceBtn.innerHTML = '<i class="mdi mdi-swap-horizontal mr-1.5"></i>切换服务';
    serviceBtn.onclick = () => showServiceSelector(url);

    controlsDiv.appendChild(previewBtn);
    controlsDiv.appendChild(serviceBtn);
    container.appendChild(controlsDiv);
}

// 显示服务选择器
function showServiceSelector(url) {
    const services = getAllSnapshotUrls(url);

    // 创建服务选择模态框
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">选择快照服务</h3>
                <button onclick="this.closest('.fixed').remove()"
                        class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    ${services.map(service => `
                        <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="font-medium text-gray-900 dark:text-white">
                                    ${service.name} ${service.primary ? '(推荐)' : ''}
                                </h4>
                                <button onclick="selectService('${service.url}', '${service.name}'); this.closest('.fixed').remove();"
                                        class="px-3 py-1.5 text-xs font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors">
                                    选择
                                </button>
                            </div>
                            <p class="text-xs text-gray-500 dark:text-gray-400 break-all">${service.url}</p>
                            <div class="mt-2">
                                <img src="${service.url}" alt="${service.name}快照"
                                     class="w-full h-32 object-cover rounded border"
                                     onload="this.style.opacity='1'"
                                     onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWKoOi9veWksei0pTwvdGV4dD48L3N2Zz4='"
                                     style="opacity: 0.5; transition: opacity 0.3s ease;">
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // 点击背景关闭
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

// 选择服务
function selectService(serviceUrl, serviceName) {
    const snapshotUrlInput = document.getElementById('addSnapshotUrl');
    snapshotUrlInput.value = serviceUrl;
    showToast(`已切换到 ${serviceName} 服务`, 'success', 2000);
}



// 预览快照
function previewSnapshot(snapshotUrl) {
    // 创建预览模态框
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50';
    modal.innerHTML = `
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">快照预览</h3>
                <button onclick="this.closest('.fixed').remove()"
                        class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div class="p-6">
                <div class="text-center">
                    <div class="loading-placeholder mb-4">
                        <div class="animate-pulse text-4xl">📸</div>
                        <div class="text-sm mt-2 opacity-70">正在加载快照...</div>
                    </div>
                    <img src="${snapshotUrl}"
                         alt="快照预览"
                         class="max-w-full max-h-96 mx-auto rounded-lg shadow-lg"
                         onload="this.previousElementSibling.style.display='none'; this.style.opacity='1';"
                         onerror="this.previousElementSibling.innerHTML='<div class=\\"text-red-500 text-4xl\\">❌</div><div class=\\"text-sm mt-2 text-red-400\\">快照加载失败</div>'; this.style.display='none';"
                         style="opacity: 0; transition: opacity 0.3s ease;">
                </div>
                <div class="mt-4 text-center">
                    <p class="text-sm text-gray-600 dark:text-gray-400 break-all">${snapshotUrl}</p>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // 点击背景关闭
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

// 表单提交处理
async function handleAddSubmit(e) {
    e.preventDefault();

    let formData = {
        title: document.getElementById('addTitle').value.trim(),
        url: document.getElementById('addUrl').value.trim(),
        description: document.getElementById('addDescription').value.trim(),
        category: document.getElementById('addCategory').value,
        status: document.getElementById('addStatus').value,
        snapshot_url: document.getElementById('addSnapshotUrl').value.trim()
    };

    if (!formData.title || !formData.url) {
        showToast('请填写标题和网址', 'error');
        return;
    }

    // 如果没有快照URL，自动生成一个
    if (!formData.snapshot_url) {
        formData.snapshot_url = generateSnapshotUrl(formData.url);
    }

    try {
        const response = await fetch('/kz/api/snapshots', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });

        if (response.ok) {
            showToast('快照收藏添加成功', 'success');
            hideAddModal();
            loadSnapshots();
        } else {
            const error = await response.json();
            showToast(error.error || '添加失败', 'error');
        }
    } catch (error) {
        console.error('添加快照失败:', error);
        showToast('添加失败', 'error');
    }
}

async function handleBatchAddSubmit(e) {
    e.preventDefault();

    const batchText = document.getElementById('batchInput').value.trim();
    const category = document.getElementById('batchCategory').value;
    const status = document.getElementById('batchStatus').value;

    if (!batchText) {
        showToast('请输入批量数据', 'error');
        return;
    }

    const lines = batchText.split('\n').filter(line => line.trim());
    const snapshots = [];

    for (const line of lines) {
        const parts = line.split('|').map(part => part.trim());
        if (parts.length >= 2) {
            const url = parts[1];
            let snapshotUrl = parts[3] || '';

            // 如果没有提供快照URL，自动生成
            if (!snapshotUrl) {
                snapshotUrl = generateSnapshotUrl(url);
            }

            snapshots.push({
                title: parts[0],
                url: url,
                description: parts[2] || '',
                snapshot_url: snapshotUrl,
                category: category,
                status: status
            });
        }
    }

    if (snapshots.length === 0) {
        showToast('没有有效的数据行', 'error');
        return;
    }

    try {
        const response = await fetch('/kz/api/snapshots/batch', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ snapshots })
        });

        if (response.ok) {
            showToast(`成功添加 ${snapshots.length} 个快照收藏`, 'success');
            hideBatchAddModal();
            loadSnapshots();
        } else {
            const error = await response.json();
            showToast(error.error || '批量添加失败', 'error');
        }
    } catch (error) {
        console.error('批量添加失败:', error);
        showToast('批量添加失败', 'error');
    }
}

// 编辑快照
function editSnapshot(id) {
    const snapshot = snapshots.find(s => s.id === id);
    if (!snapshot) {
        console.error('找不到ID为', id, '的快照');
        showToast('找不到要编辑的快照', 'error');
        return;
    }

    console.log('编辑快照:', snapshot);

    // 修改模态框标题
    const modalTitle = document.querySelector('#addModal h3');
    if (modalTitle) {
        modalTitle.textContent = '编辑快照收藏';
    }

    // 填充表单
    document.getElementById('addTitle').value = snapshot.title;
    document.getElementById('addUrl').value = snapshot.url;
    document.getElementById('addDescription').value = snapshot.description || '';
    document.getElementById('addCategory').value = snapshot.category;
    document.getElementById('addStatus').value = snapshot.status;
    document.getElementById('addSnapshotUrl').value = snapshot.snapshot_url || '';

    // 修改表单提交行为
    const form = document.getElementById('addForm');
    form.onsubmit = async (e) => {
        e.preventDefault();
        await updateSnapshot(id);
    };

    showAddModal();
}

// 更新快照
async function updateSnapshot(id) {
    let formData = {
        title: document.getElementById('addTitle').value.trim(),
        url: document.getElementById('addUrl').value.trim(),
        description: document.getElementById('addDescription').value.trim(),
        category: document.getElementById('addCategory').value,
        status: document.getElementById('addStatus').value,
        snapshot_url: document.getElementById('addSnapshotUrl').value.trim()
    };

    if (!formData.title || !formData.url) {
        showToast('请填写标题和网址', 'error');
        return;
    }

    // 如果没有快照URL，自动生成一个
    if (!formData.snapshot_url) {
        formData.snapshot_url = generateSnapshotUrl(formData.url);
    }

    try {
        const response = await fetch(`/kz/api/snapshots/${id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });

        if (response.ok) {
            showToast('快照收藏更新成功', 'success');
            hideAddModal();
            // 恢复表单提交行为
            document.getElementById('addForm').onsubmit = handleAddSubmit;
            loadSnapshots();
        } else {
            const error = await response.json();
            showToast(error.error || '更新失败', 'error');
        }
    } catch (error) {
        console.error('更新快照失败:', error);
        showToast('更新失败', 'error');
    }
}

// 显示自定义确认弹窗
function showConfirmDialog(title, message, onConfirm, onCancel = null) {
    // 创建弹窗HTML
    const dialogHTML = `
        <div id="confirmDialog" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 animate-fadeIn">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4 animate-scaleIn">
                <!-- 头部 -->
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <div class="flex items-center gap-3">
                        <div class="w-8 h-8 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">${title}</h3>
                    </div>
                </div>

                <!-- 内容 -->
                <div class="px-6 py-4">
                    <p class="text-gray-600 dark:text-gray-300 leading-relaxed">${message}</p>
                </div>

                <!-- 按钮 -->
                <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex justify-end gap-3">
                    <button id="confirmCancel" class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors">
                        取消
                    </button>
                    <button id="confirmDelete" class="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-lg transition-colors">
                        删除
                    </button>
                </div>
            </div>
        </div>
    `;

    // 添加CSS动画样式
    if (!document.getElementById('confirmDialogStyles')) {
        const styles = document.createElement('style');
        styles.id = 'confirmDialogStyles';
        styles.textContent = `
            .animate-fadeIn {
                animation: fadeIn 0.2s ease-out;
            }
            .animate-scaleIn {
                animation: scaleIn 0.2s ease-out;
            }
            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }
            @keyframes scaleIn {
                from { opacity: 0; transform: scale(0.9); }
                to { opacity: 1; transform: scale(1); }
            }
        `;
        document.head.appendChild(styles);
    }

    // 添加弹窗到页面
    document.body.insertAdjacentHTML('beforeend', dialogHTML);

    const dialog = document.getElementById('confirmDialog');
    const cancelBtn = document.getElementById('confirmCancel');
    const deleteBtn = document.getElementById('confirmDelete');

    // 取消按钮事件
    const handleCancel = () => {
        dialog.remove();
        if (onCancel) onCancel();
    };

    // 确认按钮事件
    const handleConfirm = () => {
        dialog.remove();
        if (onConfirm) onConfirm();
    };

    // 绑定事件
    cancelBtn.addEventListener('click', handleCancel);
    deleteBtn.addEventListener('click', handleConfirm);

    // 点击背景关闭
    dialog.addEventListener('click', (e) => {
        if (e.target === dialog) {
            handleCancel();
        }
    });

    // ESC键关闭
    const handleKeydown = (e) => {
        if (e.key === 'Escape') {
            handleCancel();
            document.removeEventListener('keydown', handleKeydown);
        }
    };
    document.addEventListener('keydown', handleKeydown);
}

// 删除快照
async function deleteSnapshot(id) {
    // 从当前快照列表中找到要删除的项目
    const snapshot = snapshots.find(s => s.id === id);
    const title = snapshot ? snapshot.title : '未知项目';

    showConfirmDialog(
        '确认删除',
        `确定要删除快照收藏【${title}】吗？删除后将无法恢复。`,
        async () => {
            try {
                const response = await fetch(`/kz/api/snapshots/${id}`, {
                    method: 'DELETE'
                });

                if (response.ok) {
                    showToast(`快照收藏【${title}】删除成功`, 'success');
                    loadSnapshots();
                } else {
                    const error = await response.json();
                    showToast(error.error || '删除失败', 'error');
                }
            } catch (error) {
                console.error('删除快照失败:', error);
                showToast('删除失败', 'error');
            }
        }
    );
}

// 导出Excel
async function exportExcel() {
    try {
        const searchTerm = document.getElementById('searchInput').value;
        const category = document.getElementById('categoryFilter').value;
        const status = document.getElementById('statusFilter').value;

        const params = new URLSearchParams();
        if (searchTerm) params.append('search', searchTerm);
        if (category) params.append('category', category);
        if (status) params.append('status', status);

        const response = await fetch(`/kz/api/export?${params.toString()}`);

        if (response.ok) {
            const result = await response.json();
            showToast(`导出成功：${result.fileName}`, 'success');
        } else {
            const error = await response.json();
            showToast(error.error || '导出失败', 'error');
        }
    } catch (error) {
        console.error('导出失败:', error);
        showToast('导出失败', 'error');
    }
}

// 从剪切板粘贴
async function pasteFromClipboard() {
    try {
        if (navigator.clipboard && window.isSecureContext) {
            const text = await navigator.clipboard.readText();
            if (text) {
                const batchInput = document.getElementById('batchInput');
                const currentValue = batchInput.value;

                if (currentValue.trim()) {
                    const shouldReplace = confirm('文本框中已有内容，是否要替换为剪切板内容？\n\n点击"确定"替换，点击"取消"追加到末尾。');
                    if (shouldReplace) {
                        batchInput.value = text;
                    } else {
                        batchInput.value = currentValue + (currentValue.endsWith('\n') ? '' : '\n') + text;
                    }
                } else {
                    batchInput.value = text;
                }

                batchInput.focus();
                showToast('剪切板内容已粘贴', 'success', 2000);
            } else {
                showToast('剪切板为空', 'warning');
            }
        } else {
            showToast('请使用 Ctrl+V 手动粘贴', 'info', 3000);
            document.getElementById('batchInput').focus();
        }
    } catch (error) {
        console.error('读取剪切板失败:', error);

        if (error.name === 'NotAllowedError') {
            showToast('需要剪切板权限，请使用 Ctrl+V 手动粘贴', 'warning', 4000);
        } else {
            showToast('读取剪切板失败，请使用 Ctrl+V 手动粘贴', 'error', 4000);
        }

        document.getElementById('batchInput').focus();
    }
}

// 快照加载成功处理
function handleSnapshotLoad(img, snapshotId) {
    console.log('快照加载成功:', snapshotId, img.src);
    const container = img.parentElement;
    const placeholder = container.querySelector('.loading-placeholder');
    if (placeholder) {
        placeholder.style.display = 'none';
    }
    img.style.opacity = '1';
}

// 快照错误处理
function handleSnapshotError(img, snapshotId) {
    console.warn('快照加载失败:', snapshotId, img.src);
    const container = img.parentElement;
    const snapshot = snapshots.find(s => s.id === snapshotId);
    const url = snapshot ? snapshot.url : '';

    container.innerHTML = `
        <div class="snapshot-preview error-state">
            <div class="text-red-500">📸</div>
            <div class="text-xs mt-2 opacity-70 text-red-400">加载失败</div>
            <div class="text-xs mt-1 opacity-50 break-all px-2">${img.src.substring(0, 50)}...</div>
            <button onclick="retrySnapshot(${snapshotId}, '${url}')"
                    class="mt-2 px-3 py-1 text-xs bg-red-500 text-white rounded hover:bg-red-600 transition-colors">
                重试
            </button>
            <button onclick="generateSnapshotForCard(${snapshotId}, '${url}')"
                    class="mt-1 px-3 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
                重新生成
            </button>
        </div>
    `;
}

// 为卡片生成快照
function generateSnapshotForCard(snapshotId, url) {
    const snapshotUrl = generateSnapshotUrl(url);
    console.log('为卡片生成快照:', snapshotId, url, snapshotUrl);

    // 更新数据库中的快照URL
    updateSnapshotUrl(snapshotId, snapshotUrl);
}

// 重试快照加载
function retrySnapshot(snapshotId, url) {
    const snapshot = snapshots.find(s => s.id === snapshotId);
    if (snapshot && snapshot.snapshot_url) {
        // 重新加载当前快照URL
        const container = document.querySelector(`#loading-${snapshotId}`).parentElement;
        container.innerHTML = `
            <div class="relative">
                <div class="snapshot-preview loading-placeholder" id="loading-${snapshotId}">
                    <div class="animate-pulse">📸</div>
                    <div class="text-xs mt-2 opacity-70">重新加载中...</div>
                </div>
                <img src="${snapshot.snapshot_url}?t=${Date.now()}"
                     alt="${escapeHtml(snapshot.title)}"
                     class="w-full h-48 object-cover absolute inset-0"
                     onload="handleSnapshotLoad(this, ${snapshotId})"
                     onerror="handleSnapshotError(this, ${snapshotId})"
                     style="opacity: 0; transition: opacity 0.3s ease;">
            </div>
        `;
    }
}

// 更新快照URL
async function updateSnapshotUrl(snapshotId, snapshotUrl) {
    try {
        const snapshot = snapshots.find(s => s.id === snapshotId);
        if (!snapshot) return;

        const response = await fetch(`/kz/api/snapshots/${snapshotId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                ...snapshot,
                snapshot_url: snapshotUrl
            })
        });

        if (response.ok) {
            // 更新本地数据
            snapshot.snapshot_url = snapshotUrl;
            showToast('快照地址已更新', 'success');
            // 重新渲染
            loadSnapshots();
        } else {
            showToast('更新快照地址失败', 'error');
        }
    } catch (error) {
        console.error('更新快照URL失败:', error);
        showToast('更新快照地址失败', 'error');
    }
}

// 工具函数
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

function extractDomain(url) {
    try {
        const urlObj = new URL(url);
        return urlObj.hostname;
    } catch (error) {
        return null;
    }
}

// 图标相关函数（复用主页面的逻辑）
function getMDIIconForDomain(domain) {
    const domainIconMap = {
        'google.com': 'mdi-google',
        'github.com': 'mdi-github',
        'youtube.com': 'mdi-youtube',
        'facebook.com': 'mdi-facebook',
        'twitter.com': 'mdi-twitter',
        'instagram.com': 'mdi-instagram',
        'linkedin.com': 'mdi-linkedin',
        'reddit.com': 'mdi-reddit',
        'amazon.com': 'mdi-amazon',
        'apple.com': 'mdi-apple',
        'microsoft.com': 'mdi-microsoft',
        'netflix.com': 'mdi-netflix',
        'spotify.com': 'mdi-spotify'
    };

    if (domainIconMap[domain]) {
        return domainIconMap[domain];
    }

    const mainDomain = domain.split('.').slice(-2).join('.');
    if (domainIconMap[mainDomain]) {
        return domainIconMap[mainDomain];
    }

    return null;
}

function getIconColorForDomain(domain) {
    const colorMap = {
        'google.com': '#4285F4',
        'github.com': '#181717',
        'youtube.com': '#FF0000',
        'facebook.com': '#1877F2',
        'twitter.com': '#1DA1F2',
        'instagram.com': '#E4405F',
        'linkedin.com': '#0A66C2',
        'reddit.com': '#FF4500',
        'amazon.com': '#FF9900',
        'apple.com': '#000000',
        'microsoft.com': '#00BCF2',
        'netflix.com': '#E50914',
        'spotify.com': '#1DB954'
    };

    if (colorMap[domain]) {
        return colorMap[domain];
    }

    const mainDomain = domain.split('.').slice(-2).join('.');
    if (colorMap[mainDomain]) {
        return colorMap[mainDomain];
    }

    return '#6B7280';
}

function generateColorfulSVGIcon(title, domain) {
    const firstLetter = (title || domain || 'W').charAt(0).toUpperCase();

    const colorSchemes = [
        { bg: '#FF6B6B', text: '#FFFFFF' },
        { bg: '#4ECDC4', text: '#FFFFFF' },
        { bg: '#45B7D1', text: '#FFFFFF' },
        { bg: '#96CEB4', text: '#FFFFFF' },
        { bg: '#FFEAA7', text: '#2D3436' },
        { bg: '#DDA0DD', text: '#FFFFFF' },
        { bg: '#98D8C8', text: '#2D3436' },
        { bg: '#F7DC6F', text: '#2D3436' },
        { bg: '#BB8FCE', text: '#FFFFFF' },
        { bg: '#85C1E9', text: '#FFFFFF' },
        { bg: '#F8C471', text: '#2D3436' },
        { bg: '#82E0AA', text: '#2D3436' }
    ];

    const colorIndex = firstLetter.charCodeAt(0) % colorSchemes.length;
    const colors = colorSchemes[colorIndex];

    const svg = `
        <svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="grad-${firstLetter}" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:${colors.bg};stop-opacity:1" />
                    <stop offset="100%" style="stop-color:${adjustBrightness(colors.bg, -20)};stop-opacity:1" />
                </linearGradient>
            </defs>
            <circle cx="10" cy="10" r="9" fill="url(#grad-${firstLetter})" stroke="${adjustBrightness(colors.bg, -30)}" stroke-width="1"/>
            <text x="10" y="14" font-family="Arial, sans-serif" font-size="10" font-weight="bold" text-anchor="middle" fill="${colors.text}">
                ${firstLetter}
            </text>
        </svg>
    `;

    return `data:image/svg+xml;base64,${btoa(svg)}`;
}

function adjustBrightness(hex, percent) {
    hex = hex.replace('#', '');

    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);

    const newR = Math.max(0, Math.min(255, r + (r * percent / 100)));
    const newG = Math.max(0, Math.min(255, g + (g * percent / 100)));
    const newB = Math.max(0, Math.min(255, b + (b * percent / 100)));

    return `#${Math.round(newR).toString(16).padStart(2, '0')}${Math.round(newG).toString(16).padStart(2, '0')}${Math.round(newB).toString(16).padStart(2, '0')}`;
}











// 调试函数 - 检查按钮状态
function debugButtons() {
    const editButtons = document.querySelectorAll('[data-snapshot-id]');
    const snapshotCards = document.querySelectorAll('#snapshotGrid > div');

    console.log('=== 按钮调试信息 ===');
    console.log('快照卡片数量:', snapshotCards.length);
    console.log('操作按钮数量:', editButtons.length);
    console.log('当前快照数据:', snapshots);
    console.log('筛选后快照数据:', filteredSnapshots);

    editButtons.forEach((button, index) => {
        const snapshotId = button.getAttribute('data-snapshot-id');
        console.log(`按钮 ${index + 1}: ID=${snapshotId}, 类型=${button.textContent.trim()}`);
    });

    if (editButtons.length === 0 && snapshotCards.length > 0) {
        console.error('警告：有快照卡片但没有操作按钮！');

        // 检查HTML结构
        snapshotCards.forEach((card, index) => {
            console.log(`卡片 ${index + 1} HTML:`, card.outerHTML.substring(0, 500) + '...');
        });
    }

    return {
        cards: snapshotCards.length,
        buttons: editButtons.length,
        snapshots: snapshots.length,
        filtered: filteredSnapshots.length
    };
}

// 将调试函数暴露到全局
window.debugButtons = debugButtons;

// 通知函数
function showToast(message, type = 'info', duration = 3000) {
    const toast = document.getElementById('toast');
    const toastMessage = document.getElementById('toastMessage');
    const toastIcon = document.getElementById('toastIcon');

    // 设置图标和样式
    const icons = {
        success: '<svg class="w-6 h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>',
        error: '<svg class="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>',
        warning: '<svg class="w-6 h-6 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path></svg>',
        info: '<svg class="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>'
    };

    toastIcon.innerHTML = icons[type] || icons.info;
    toastMessage.textContent = message;

    // 显示通知
    toast.classList.remove('hidden');

    // 自动隐藏
    setTimeout(() => {
        toast.classList.add('hidden');
    }, duration);
}
