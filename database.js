const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// 延迟加载自动导出模块，避免循环依赖
let autoExport = null;
function getAutoExport() {
    if (!autoExport) {
        autoExport = require('./autoExport');
    }
    return autoExport;
}

// 创建数据库连接
const dbPath = path.join(__dirname, 'bookmarks.db');
const db = new sqlite3.Database(dbPath);

// 初始化数据库表
function initDatabase() {
    const createTableSQL = `
        CREATE TABLE IF NOT EXISTS bookmarks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            url TEXT NOT NULL UNIQUE,
            description TEXT,
            category TEXT DEFAULT '默认',
            status TEXT DEFAULT '默认',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    `;

    db.run(createTableSQL, (err) => {
        if (err) {
            console.error('创建表失败:', err.message);
        } else {
            console.log('数据库表初始化成功');
            // 为现有表添加状态字段（如果不存在）
            addStatusColumnIfNotExists();
        }
    });
}

// 为现有表添加状态字段（如果不存在）
function addStatusColumnIfNotExists() {
    const checkColumnSQL = "PRAGMA table_info(bookmarks)";
    db.all(checkColumnSQL, [], (err, columns) => {
        if (err) {
            console.error('检查表结构失败:', err.message);
            return;
        }

        const hasStatusColumn = columns.some(column => column.name === 'status');
        if (!hasStatusColumn) {
            const addColumnSQL = "ALTER TABLE bookmarks ADD COLUMN status TEXT DEFAULT '默认'";
            db.run(addColumnSQL, (err) => {
                if (err) {
                    console.error('添加状态字段失败:', err.message);
                } else {
                    console.log('状态字段添加成功');
                }
            });
        }
    });
}

// 获取所有收藏
function getAllBookmarks(callback) {
    const sql = 'SELECT * FROM bookmarks ORDER BY created_at DESC';
    db.all(sql, [], callback);
}

// 获取收藏（支持分页）
function getBookmarksPaginated(page = 1, limit = 20, callback) {
    const offset = (page - 1) * limit;
    const sql = 'SELECT * FROM bookmarks ORDER BY created_at DESC LIMIT ? OFFSET ?';
    db.all(sql, [limit, offset], callback);
}

// 获取收藏总数
function getBookmarksCount(callback) {
    const sql = 'SELECT COUNT(*) as total FROM bookmarks';
    db.get(sql, [], callback);
}

// 根据分类获取收藏
function getBookmarksByCategory(category, callback) {
    const sql = 'SELECT * FROM bookmarks WHERE category = ? ORDER BY created_at DESC';
    db.all(sql, [category], callback);
}

// 根据分类获取收藏（支持分页）
function getBookmarksByCategoryPaginated(category, page = 1, limit = 20, callback) {
    const offset = (page - 1) * limit;
    const sql = 'SELECT * FROM bookmarks WHERE category = ? ORDER BY created_at DESC LIMIT ? OFFSET ?';
    db.all(sql, [category, limit, offset], callback);
}

// 获取分类收藏总数
function getBookmarksByCategoryCount(category, callback) {
    const sql = 'SELECT COUNT(*) as total FROM bookmarks WHERE category = ?';
    db.get(sql, [category], callback);
}

// 检查URL是否已存在（在收藏和快照中）
function checkUrlExists(url, callback) {
    // 检查普通收藏中是否存在
    const checkBookmarksSql = 'SELECT COUNT(*) as count FROM bookmarks WHERE url = ?';
    db.get(checkBookmarksSql, [url], (err, bookmarkResult) => {
        if (err) {
            callback(err, null);
            return;
        }

        if (bookmarkResult.count > 0) {
            callback(null, { exists: true, type: 'bookmark' });
            return;
        }

        // 检查快照收藏中是否存在
        const snapshotDb = require('./snapshotDatabase');
        const checkSnapshotsSql = 'SELECT COUNT(*) as count FROM snapshot_bookmarks WHERE url = ?';
        snapshotDb.db.get(checkSnapshotsSql, [url], (err, snapshotResult) => {
            if (err) {
                callback(err, null);
                return;
            }

            if (snapshotResult.count > 0) {
                callback(null, { exists: true, type: 'snapshot' });
                return;
            }

            callback(null, { exists: false });
        });
    });
}

// 添加新收藏
function addBookmark(bookmark, callback) {
    const { title, url, description, category, status } = bookmark;

    // 先检查URL是否已存在
    checkUrlExists(url, (err, result) => {
        if (err) {
            callback(err, null);
            return;
        }

        if (result.exists) {
            const errorMsg = result.type === 'bookmark' ?
                '该网址已存在于收藏夹中' :
                '该网址已存在于快照收藏中';
            callback(new Error(errorMsg), null);
            return;
        }

        // URL不存在，可以添加
        const sql = `
            INSERT INTO bookmarks (title, url, description, category, status)
            VALUES (?, ?, ?, ?, ?)
        `;
        db.run(sql, [title, url, description, category || '默认', status || '默认'], function(err) {
            if (!err) {
                // 触发自动导出
                try {
                    getAutoExport().markDataChanged();
                } catch (e) {
                    console.error('触发自动导出失败:', e);
                }
            }
            callback(err, this.lastID);
        });
    });
}

// 更新收藏
function updateBookmark(id, bookmark, callback) {
    const { title, url, description, category, status } = bookmark;
    const sql = `
        UPDATE bookmarks
        SET title = ?, url = ?, description = ?, category = ?, status = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
    `;
    db.run(sql, [title, url, description, category, status, id], function(err) {
        if (!err) {
            // 触发自动导出
            try {
                getAutoExport().markDataChanged();
            } catch (e) {
                console.error('触发自动导出失败:', e);
            }
        }
        callback(err);
    });
}

// 删除收藏
function deleteBookmark(id, callback) {
    const sql = 'DELETE FROM bookmarks WHERE id = ?';
    db.run(sql, [id], function(err) {
        if (!err) {
            // 触发自动导出
            try {
                getAutoExport().markDataChanged();
            } catch (e) {
                console.error('触发自动导出失败:', e);
            }
        }
        callback(err);
    });
}

// 获取所有分类
function getAllCategories(callback) {
    const sql = 'SELECT DISTINCT category FROM bookmarks ORDER BY category';
    db.all(sql, [], callback);
}

// 获取所有状态
function getAllStatuses(callback) {
    const sql = 'SELECT DISTINCT status FROM bookmarks ORDER BY status';
    db.all(sql, [], callback);
}

// 根据状态获取收藏
function getBookmarksByStatus(status, callback) {
    const sql = 'SELECT * FROM bookmarks WHERE status = ? ORDER BY created_at DESC';
    db.all(sql, [status], callback);
}

// 根据状态获取收藏（支持分页）
function getBookmarksByStatusPaginated(status, page = 1, limit = 20, callback) {
    const offset = (page - 1) * limit;
    const sql = 'SELECT * FROM bookmarks WHERE status = ? ORDER BY created_at DESC LIMIT ? OFFSET ?';
    db.all(sql, [status, limit, offset], callback);
}

// 获取状态收藏总数
function getBookmarksByStatusCount(status, callback) {
    const sql = 'SELECT COUNT(*) as total FROM bookmarks WHERE status = ?';
    db.get(sql, [status], callback);
}

// 搜索收藏
function searchBookmarks(keyword, callback) {
    const sql = `
        SELECT * FROM bookmarks
        WHERE title LIKE ? OR description LIKE ? OR url LIKE ?
        ORDER BY created_at DESC
    `;
    const searchTerm = `%${keyword}%`;
    db.all(sql, [searchTerm, searchTerm, searchTerm], callback);
}

// 搜索收藏（支持分页）
function searchBookmarksPaginated(keyword, page = 1, limit = 20, callback) {
    const offset = (page - 1) * limit;
    const sql = `
        SELECT * FROM bookmarks
        WHERE title LIKE ? OR description LIKE ? OR url LIKE ?
        ORDER BY created_at DESC LIMIT ? OFFSET ?
    `;
    const searchTerm = `%${keyword}%`;
    db.all(sql, [searchTerm, searchTerm, searchTerm, limit, offset], callback);
}

// 获取搜索结果总数
function getSearchBookmarksCount(keyword, callback) {
    const sql = `
        SELECT COUNT(*) as total FROM bookmarks
        WHERE title LIKE ? OR description LIKE ? OR url LIKE ?
    `;
    const searchTerm = `%${keyword}%`;
    db.get(sql, [searchTerm, searchTerm, searchTerm], callback);
}

module.exports = {
    initDatabase,
    getAllBookmarks,
    getBookmarksPaginated,
    getBookmarksCount,
    getBookmarksByCategory,
    getBookmarksByCategoryPaginated,
    getBookmarksByCategoryCount,
    getBookmarksByStatus,
    getBookmarksByStatusPaginated,
    getBookmarksByStatusCount,
    checkUrlExists,
    addBookmark,
    updateBookmark,
    deleteBookmark,
    getAllCategories,
    getAllStatuses,
    searchBookmarks,
    searchBookmarksPaginated,
    getSearchBookmarksCount,
    db: db
};
