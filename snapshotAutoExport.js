const XLSX = require('xlsx');
const fs = require('fs');
const path = require('path');
const { snapshotBookmarkOperations } = require('./snapshotDatabase');

class SnapshotAutoExport {
    constructor() {
        this.exportDir = path.join(__dirname, 'exports');
        this.exportInterval = 3 * 60 * 1000; // 3分钟
        this.isExporting = false;
        this.lastExportTime = null;
        
        // 确保导出目录存在
        if (!fs.existsSync(this.exportDir)) {
            fs.mkdirSync(this.exportDir, { recursive: true });
        }
        
        console.log('初始化快照自动导出系统...');
        
        // 启动时立即执行一次导出
        this.exportData().then(() => {
            console.log('快照初始导出完成');
            // 启动定时导出
            this.startAutoExport();
        });
    }
    
    // 启动自动导出
    startAutoExport() {
        setInterval(() => {
            this.exportData();
        }, this.exportInterval);
    }
    
    // 导出数据到Excel
    async exportData() {
        if (this.isExporting) {
            console.log('快照导出正在进行中，跳过本次导出');
            return;
        }
        
        this.isExporting = true;
        
        try {
            const bookmarks = await this.getSnapshotBookmarks();
            
            if (bookmarks.length === 0) {
                console.log('没有快照收藏数据需要导出');
                this.isExporting = false;
                return;
            }
            
            // 准备Excel数据
            const excelData = bookmarks.map(bookmark => ({
                'ID': bookmark.id,
                '标题': bookmark.title,
                '网址': bookmark.url,
                '描述': bookmark.description || '',
                '分类': bookmark.category,
                '状态': bookmark.status,
                '快照地址': bookmark.snapshot_url || '',
                '创建时间': bookmark.created_at,
                '更新时间': bookmark.updated_at
            }));
            
            // 创建工作簿
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.json_to_sheet(excelData);
            
            // 设置列宽
            const colWidths = [
                { wch: 5 },   // ID
                { wch: 30 },  // 标题
                { wch: 50 },  // 网址
                { wch: 30 },  // 描述
                { wch: 10 },  // 分类
                { wch: 8 },   // 状态
                { wch: 50 },  // 快照地址
                { wch: 20 },  // 创建时间
                { wch: 20 }   // 更新时间
            ];
            ws['!cols'] = colWidths;
            
            // 添加工作表
            XLSX.utils.book_append_sheet(wb, ws, '快照收藏列表');
            
            // 生成文件名
            const fileName = 'snapshot_bookmarks_backup.xlsx';
            const filePath = path.join(this.exportDir, fileName);
            
            // 写入文件
            XLSX.writeFile(wb, filePath);
            
            this.lastExportTime = new Date();
            const timeStr = this.lastExportTime.toLocaleString('zh-CN');
            console.log(`[${timeStr}] 快照自动导出完成: ${bookmarks.length} 条记录 -> ${filePath}`);
            
        } catch (error) {
            console.error('快照自动导出失败:', error);
        } finally {
            this.isExporting = false;
        }
    }
    
    // 获取快照收藏数据
    getSnapshotBookmarks() {
        return new Promise((resolve, reject) => {
            snapshotBookmarkOperations.getAll((err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows || []);
                }
            });
        });
    }
    
    // 手动导出（带筛选条件）
    async manualExport(filters = {}) {
        try {
            let bookmarks = await this.getSnapshotBookmarks();
            
            // 应用筛选条件
            if (filters.search) {
                const searchTerm = filters.search.toLowerCase();
                bookmarks = bookmarks.filter(bookmark => 
                    bookmark.title.toLowerCase().includes(searchTerm) ||
                    bookmark.url.toLowerCase().includes(searchTerm) ||
                    (bookmark.description && bookmark.description.toLowerCase().includes(searchTerm))
                );
            }
            
            if (filters.category && filters.category !== '全部') {
                bookmarks = bookmarks.filter(bookmark => bookmark.category === filters.category);
            }
            
            if (filters.status && filters.status !== '全部') {
                bookmarks = bookmarks.filter(bookmark => bookmark.status === filters.status);
            }
            
            if (bookmarks.length === 0) {
                throw new Error('没有符合条件的快照收藏数据');
            }
            
            // 准备Excel数据
            const excelData = bookmarks.map(bookmark => ({
                'ID': bookmark.id,
                '标题': bookmark.title,
                '网址': bookmark.url,
                '描述': bookmark.description || '',
                '分类': bookmark.category,
                '状态': bookmark.status,
                '快照地址': bookmark.snapshot_url || '',
                '创建时间': bookmark.created_at,
                '更新时间': bookmark.updated_at
            }));
            
            // 创建工作簿
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.json_to_sheet(excelData);
            
            // 设置列宽
            const colWidths = [
                { wch: 5 },   // ID
                { wch: 30 },  // 标题
                { wch: 50 },  // 网址
                { wch: 30 },  // 描述
                { wch: 10 },  // 分类
                { wch: 8 },   // 状态
                { wch: 50 },  // 快照地址
                { wch: 20 },  // 创建时间
                { wch: 20 }   // 更新时间
            ];
            ws['!cols'] = colWidths;
            
            // 添加工作表
            XLSX.utils.book_append_sheet(wb, ws, '快照收藏列表');
            
            // 生成带时间戳的文件名
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
            const fileName = `snapshot_bookmarks_${timestamp}.xlsx`;
            const filePath = path.join(this.exportDir, fileName);
            
            // 写入文件
            XLSX.writeFile(wb, filePath);
            
            return {
                success: true,
                fileName: fileName,
                filePath: filePath,
                count: bookmarks.length
            };
            
        } catch (error) {
            console.error('快照手动导出失败:', error);
            throw error;
        }
    }
    
    // 获取导出状态
    getExportStatus() {
        return {
            isExporting: this.isExporting,
            lastExportTime: this.lastExportTime,
            exportInterval: this.exportInterval
        };
    }
}

module.exports = SnapshotAutoExport;
