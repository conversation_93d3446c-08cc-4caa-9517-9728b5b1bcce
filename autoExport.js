const XLSX = require('xlsx');
const fs = require('fs');
const path = require('path');
const db = require('./database');

// 导出文件夹路径
const EXPORT_DIR = path.join(__dirname, 'exports');
const EXPORT_FILE = path.join(EXPORT_DIR, 'bookmarks_backup.xlsx');

// 确保导出文件夹存在
function ensureExportDir() {
    if (!fs.existsSync(EXPORT_DIR)) {
        fs.mkdirSync(EXPORT_DIR, { recursive: true });
        console.log('创建导出文件夹:', EXPORT_DIR);
    }
}

// 格式化日期用于Excel
function formatDateForExcel(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

// 导出数据到Excel
function exportToExcel() {
    return new Promise((resolve, reject) => {
        // 获取所有收藏数据
        db.getAllBookmarks((err, bookmarks) => {
            if (err) {
                console.error('获取收藏数据失败:', err);
                reject(err);
                return;
            }

            try {
                // 确保导出文件夹存在
                ensureExportDir();

                // 准备Excel数据
                const excelData = bookmarks.map(bookmark => ({
                    'ID': bookmark.id,
                    '标题': bookmark.title,
                    '网址': bookmark.url,
                    '分类': bookmark.category,
                    '状态': bookmark.status,
                    '描述': bookmark.description || '',
                    '创建时间': formatDateForExcel(bookmark.created_at),
                    '更新时间': formatDateForExcel(bookmark.updated_at)
                }));

                // 创建工作簿
                const wb = XLSX.utils.book_new();
                const ws = XLSX.utils.json_to_sheet(excelData);

                // 设置列宽
                const colWidths = [
                    { wch: 8 },  // ID
                    { wch: 30 }, // 标题
                    { wch: 50 }, // 网址
                    { wch: 15 }, // 分类
                    { wch: 10 }, // 状态
                    { wch: 40 }, // 描述
                    { wch: 20 }, // 创建时间
                    { wch: 20 }  // 更新时间
                ];
                ws['!cols'] = colWidths;

                // 添加工作表到工作簿
                XLSX.utils.book_append_sheet(wb, ws, '网址收藏');

                // 删除旧文件（如果存在）
                if (fs.existsSync(EXPORT_FILE)) {
                    fs.unlinkSync(EXPORT_FILE);
                }

                // 写入新文件
                XLSX.writeFile(wb, EXPORT_FILE);

                const now = new Date().toLocaleString('zh-CN');
                console.log(`[${now}] 自动导出完成: ${bookmarks.length} 条记录 -> ${EXPORT_FILE}`);
                
                resolve({
                    success: true,
                    count: bookmarks.length,
                    file: EXPORT_FILE,
                    timestamp: now
                });

            } catch (error) {
                console.error('导出Excel失败:', error);
                reject(error);
            }
        });
    });
}

// 数据变化标记
let dataChanged = false;
let exportTimer = null;

// 标记数据已变化
function markDataChanged() {
    dataChanged = true;

    // 清除之前的定时器
    if (exportTimer) {
        clearTimeout(exportTimer);
    }

    console.log('数据已变化，将在3分钟后自动导出备份...');
    
    // 设置新的定时器，3分钟后导出（防止频繁操作时重复导出）
    exportTimer = setTimeout(() => {
        if (dataChanged) {
            exportToExcel()
                .then(result => {
                    dataChanged = false;
                })
                .catch(error => {
                    console.error('自动导出失败:', error);
                });
        }
    }, 3 * 60 * 1000); // 3分钟延迟
}

// 启动时执行一次导出
function initAutoExport() {
    console.log('初始化自动导出系统...');
    
    // 确保导出文件夹存在
    ensureExportDir();
    
    // 启动时导出一次
    exportToExcel()
        .then(result => {
            console.log('初始导出完成');
        })
        .catch(error => {
            console.error('初始导出失败:', error);
        });
}

module.exports = {
    exportToExcel,
    markDataChanged,
    initAutoExport,
    EXPORT_DIR,
    EXPORT_FILE
};
