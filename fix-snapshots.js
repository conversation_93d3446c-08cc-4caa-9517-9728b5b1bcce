// 快照修复脚本
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// 数据库路径
const dbPath = path.join(__dirname, 'snapshots.db');

// 快照服务配置
const SNAPSHOT_SERVICES = [
    {
        name: 'WordPress.com mShots',
        url: (targetUrl) => `https://s0.wp.com/mshots/v1/${targetUrl}?w=1200&h=800`
    }
];

function extractDomain(url) {
    try {
        const urlObj = new URL(url);
        return urlObj.hostname;
    } catch (error) {
        return null;
    }
}

function generateSnapshotUrl(url, serviceIndex = 0) {
    if (!url) return '';
    
    // 确保URL有协议
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
        url = 'https://' + url;
    }
    
    const service = SNAPSHOT_SERVICES[serviceIndex] || SNAPSHOT_SERVICES[0];
    return service.url(url);
}

// 修复所有快照
function fixAllSnapshots() {
    const db = new sqlite3.Database(dbPath);
    
    console.log('🔍 检查快照数据库...');
    
    // 获取所有快照
    db.all('SELECT * FROM snapshot_bookmarks', (err, rows) => {
        if (err) {
            console.error('❌ 读取数据库失败:', err);
            return;
        }
        
        console.log(`📊 找到 ${rows.length} 个快照记录`);
        
        let fixedCount = 0;
        let needsFixCount = 0;
        
        rows.forEach((row, index) => {
            const needsFix = !row.snapshot_url || row.snapshot_url.trim() === '';
            
            if (needsFix) {
                needsFixCount++;
                const newSnapshotUrl = generateSnapshotUrl(row.url);
                
                console.log(`🔧 修复快照 ${index + 1}: ${row.title}`);
                console.log(`   URL: ${row.url}`);
                console.log(`   新快照: ${newSnapshotUrl}`);
                
                // 更新数据库
                db.run(
                    'UPDATE snapshot_bookmarks SET snapshot_url = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                    [newSnapshotUrl, row.id],
                    function(err) {
                        if (err) {
                            console.error(`❌ 更新失败 (ID: ${row.id}):`, err);
                        } else {
                            fixedCount++;
                            console.log(`✅ 已修复 (ID: ${row.id})`);
                            
                            // 如果所有需要修复的都完成了
                            if (fixedCount === needsFixCount) {
                                console.log(`\n🎉 修复完成！共修复 ${fixedCount} 个快照`);
                                db.close();
                            }
                        }
                    }
                );
            } else {
                console.log(`✅ 快照正常 ${index + 1}: ${row.title}`);
            }
        });
        
        if (needsFixCount === 0) {
            console.log('🎉 所有快照都正常，无需修复！');
            db.close();
        }
    });
}

// 添加测试数据
function addTestData() {
    const db = new sqlite3.Database(dbPath);
    
    const testData = [
        {
            title: 'Google',
            url: 'https://www.google.com',
            description: '全球最大的搜索引擎',
            category: '搜索',
            status: '默认'
        },
        {
            title: 'GitHub',
            url: 'https://github.com',
            description: '全球最大的代码托管平台',
            category: '开发',
            status: '默认'
        },
        {
            title: '百度',
            url: 'https://www.baidu.com',
            description: '中国最大的搜索引擎',
            category: '搜索',
            status: '默认'
        },
        {
            title: 'YouTube',
            url: 'https://www.youtube.com',
            description: '全球最大的视频分享网站',
            category: '视频',
            status: '默认'
        },
        {
            title: '淘宝',
            url: 'https://www.taobao.com',
            description: '中国最大的电商平台',
            category: '购物',
            status: '默认'
        }
    ];
    
    console.log('📝 添加测试数据...');
    
    let addedCount = 0;
    testData.forEach((item, index) => {
        const snapshotUrl = generateSnapshotUrl(item.url);
        
        db.run(
            `INSERT INTO snapshot_bookmarks (title, url, description, category, status, snapshot_url, created_at, updated_at) 
             VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
            [item.title, item.url, item.description, item.category, item.status, snapshotUrl],
            function(err) {
                if (err) {
                    console.error(`❌ 添加失败 ${item.title}:`, err);
                } else {
                    addedCount++;
                    console.log(`✅ 已添加: ${item.title} (ID: ${this.lastID})`);
                    console.log(`   快照: ${snapshotUrl}`);
                    
                    if (addedCount === testData.length) {
                        console.log(`\n🎉 测试数据添加完成！共添加 ${addedCount} 个快照`);
                        db.close();
                    }
                }
            }
        );
    });
}

// 清空所有数据
function clearAllData() {
    const db = new sqlite3.Database(dbPath);
    
    console.log('🗑️ 清空所有快照数据...');
    
    db.run('DELETE FROM snapshot_bookmarks', function(err) {
        if (err) {
            console.error('❌ 清空失败:', err);
        } else {
            console.log(`✅ 已清空 ${this.changes} 条记录`);
        }
        db.close();
    });
}

// 显示所有数据
function showAllData() {
    const db = new sqlite3.Database(dbPath);
    
    console.log('📋 显示所有快照数据...');
    
    db.all('SELECT * FROM snapshot_bookmarks ORDER BY created_at DESC', (err, rows) => {
        if (err) {
            console.error('❌ 读取失败:', err);
            return;
        }
        
        console.log(`\n📊 共有 ${rows.length} 个快照:`);
        console.log('=' .repeat(80));
        
        rows.forEach((row, index) => {
            console.log(`${index + 1}. ${row.title}`);
            console.log(`   URL: ${row.url}`);
            console.log(`   快照: ${row.snapshot_url || '无'}`);
            console.log(`   分类: ${row.category} | 状态: ${row.status}`);
            console.log(`   创建: ${row.created_at}`);
            console.log('-'.repeat(80));
        });
        
        db.close();
    });
}

// 命令行参数处理
const command = process.argv[2];

switch (command) {
    case 'fix':
        fixAllSnapshots();
        break;
    case 'add':
        addTestData();
        break;
    case 'clear':
        clearAllData();
        break;
    case 'show':
        showAllData();
        break;
    default:
        console.log('🛠️ 快照修复工具');
        console.log('');
        console.log('使用方法:');
        console.log('  node fix-snapshots.js fix    - 修复所有缺失的快照URL');
        console.log('  node fix-snapshots.js add    - 添加测试数据');
        console.log('  node fix-snapshots.js show   - 显示所有数据');
        console.log('  node fix-snapshots.js clear  - 清空所有数据');
        console.log('');
        console.log('推荐使用顺序:');
        console.log('1. node fix-snapshots.js add   (添加测试数据)');
        console.log('2. node fix-snapshots.js show  (查看数据)');
        console.log('3. 访问 http://localhost:3004/kz 查看效果');
        break;
}
