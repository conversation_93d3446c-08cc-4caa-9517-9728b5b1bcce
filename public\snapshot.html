<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📸 书签快照列表</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <!-- MDI 图标库 -->
    <link rel="stylesheet" href="materialdesignicons.min.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6'
                    }
                }
            }
        }
    </script>
    <style>
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .snapshot-preview {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 200px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 48px;
        }
        .loading-placeholder {
            background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
        }
        .error-state {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        }

        /* 确保按钮在小屏幕上也能正确显示 */
        @media (max-width: 640px) {
            .snapshot-card-actions {
                flex-direction: column;
                gap: 0.5rem;
            }
            .snapshot-card-actions > div {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body class="bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
    <!-- 导航栏 -->
    <nav class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-4">
                    <h1 class="text-xl font-bold text-gray-900 dark:text-white">📸 书签快照列表</h1>
                    <span class="text-sm text-gray-500 dark:text-gray-400">管理和预览你的网页快照</span>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="/" class="inline-flex items-center px-3 py-2 text-sm font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors duration-200"
                       title="返回主页">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5v4"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 5v4"></path>
                        </svg>
                        🔖 收藏列表
                    </a>
                    <button id="themeToggle" class="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                        <svg class="w-5 h-5 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 操作栏 -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                <!-- 搜索和筛选 -->
                <div class="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4 flex-1">
                    <div class="relative flex-1 max-w-md">
                        <input type="text" id="searchInput" placeholder="搜索标题、网址或描述..."
                               class="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent outline-none transition-all">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <select id="categoryFilter" class="px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent outline-none transition-all">
                        <option value="">全部分类</option>
                    </select>
                    <select id="statusFilter" class="px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent outline-none transition-all">
                        <option value="">全部状态</option>
                    </select>
                </div>
                
                <!-- 操作按钮 -->
                <div class="flex space-x-3 flex-wrap">
                    <button onclick="showAddModal()" class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 font-medium">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        添加快照
                    </button>
                    <button onclick="showBatchAddModal()" class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 font-medium">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        批量添加
                    </button>

                    <button onclick="exportExcel()" class="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors duration-200 font-medium">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        导出Excel
                    </button>

                    <button onclick="window.debugButtons && window.debugButtons()" class="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors duration-200 font-medium" title="调试按钮状态">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        调试
                    </button>
                </div>
            </div>
        </div>

        <!-- 快照卡片网格 -->
        <div id="snapshotGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            <!-- 快照卡片将在这里动态生成 -->
        </div>

        <!-- 分页 -->
        <div id="pagination" class="mt-8 flex justify-center">
            <!-- 分页控件将在这里动态生成 -->
        </div>
    </div>

    <!-- 添加快照模态框 -->
    <div id="addModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">添加快照收藏</h3>
            </div>
            <form id="addForm" class="p-6 space-y-4">
                <div>
                    <label for="addTitle" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        标题 <span class="text-red-500">*</span>
                    </label>
                    <input type="text" id="addTitle" required
                           class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent outline-none transition-all">
                </div>
                <div>
                    <label for="addUrl" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        网址 <span class="text-red-500">*</span>
                    </label>
                    <input type="url" id="addUrl" required
                           class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent outline-none transition-all">
                </div>
                <div>
                    <div class="flex items-center justify-between mb-2">
                        <label for="addSnapshotUrl" class="text-sm font-medium text-gray-700 dark:text-gray-300">
                            快照地址
                        </label>
                        <button type="button" onclick="autoFillSnapshotUrl()"
                                class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors duration-200"
                                title="根据网址自动生成快照地址">
                            <i class="mdi mdi-auto-fix mr-1.5"></i>
                            自动生成
                        </button>
                    </div>
                    <input type="url" id="addSnapshotUrl"
                           placeholder="留空将自动生成快照地址"
                           class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent outline-none transition-all">
                </div>
                <div>
                    <label for="addDescription" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">描述</label>
                    <textarea id="addDescription" rows="3"
                              class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent outline-none transition-all resize-none"></textarea>
                </div>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label for="addCategory" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">分类</label>
                        <select id="addCategory" class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent outline-none transition-all">
                        </select>
                    </div>
                    <div>
                        <label for="addStatus" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">状态</label>
                        <select id="addStatus" class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent outline-none transition-all">
                        </select>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="hideAddModal()" class="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200">
                        取消
                    </button>
                    <button type="submit" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-blue-600 transition-colors duration-200">
                        保存
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 批量添加模态框 -->
    <div id="batchAddModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">批量添加快照收藏</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    格式：标题|网址|描述|快照地址（每行一条，描述和快照地址可选，留空将自动生成快照）
                </p>
            </div>
            <form id="batchAddForm" class="p-6 space-y-4">
                <div>
                    <div class="flex items-center justify-between mb-2">
                        <label for="batchInput" class="text-sm font-medium text-gray-700 dark:text-gray-300">
                            批量输入 <span class="text-red-500">*</span>
                        </label>
                        <button type="button" onclick="pasteFromClipboard()" 
                                class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors duration-200"
                                title="从剪切板粘贴内容">
                            <i class="mdi mdi-clipboard-text-outline mr-1.5"></i>
                            从剪切板粘贴
                        </button>
                    </div>
                    <textarea id="batchInput" rows="15" required
                              placeholder="示例：
Google|https://www.google.com|搜索引擎
GitHub|https://github.com|代码托管
YouTube|https://www.youtube.com|视频网站

格式：标题|网址|描述|快照地址
（描述和快照地址可选，留空将自动生成）"
                              class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent outline-none transition-all resize-none font-mono text-sm"></textarea>
                </div>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label for="batchCategory" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">统一分类</label>
                        <select id="batchCategory" class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent outline-none transition-all">
                        </select>
                    </div>
                    <div>
                        <label for="batchStatus" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">统一状态</label>
                        <select id="batchStatus" class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent outline-none transition-all">
                        </select>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="hideBatchAddModal()" class="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200">
                        取消
                    </button>
                    <button type="submit" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200">
                        批量添加
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 通知组件 -->
    <div id="toast" class="fixed top-4 right-4 z-50 hidden">
        <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-4 max-w-sm">
            <div class="flex items-center">
                <div id="toastIcon" class="flex-shrink-0 w-6 h-6 mr-3"></div>
                <div id="toastMessage" class="text-sm font-medium text-gray-900 dark:text-white"></div>
            </div>
        </div>
    </div>

    <script src="snapshot-script.js"></script>
</body>
</html>
