# 🔖 网址收藏系统

一个简洁美观的网址收藏管理系统，支持分类管理、批量添加、自动备份等功能。

## ✨ 主要功能

- 📚 **收藏管理** - 添加、编辑、删除网址收藏
- 📸 **快照列表** - 卡片式书签快照预览（/kz页面）
- 🏷️ **分类管理** - 自定义分类，便于整理
- 📊 **状态管理** - 默认、未读、已读、收藏、临时状态
- 📝 **批量添加** - 支持批量导入收藏，提高效率
- 🔍 **搜索筛选** - 按标题、分类、状态快速筛选
- 🎨 **网站图标** - 自动显示网站图标，美观直观
- 📤 **自动备份** - 自动导出Excel备份文件
- 🌙 **主题切换** - 支持明暗主题切换
- 📱 **响应式设计** - 完美适配各种设备

## 🚀 快速开始

### 环境要求
- Node.js (建议 v14 或更高版本)

### 安装启动
1. 下载项目文件
2. 双击 `start_server.bat` 启动服务器
3. 访问 http://localhost:3004

### 页面访问
- **主页面**：http://localhost:3004 （列表式收藏管理）
- **快照页面**：http://localhost:3004/kz （卡片式快照预览）

### 停止服务
- 双击 `stop_server.bat`
- 或在服务器窗口按 `Ctrl+C`

## 📁 项目结构

```
网址收藏系统/
├── server.js              # 服务器主文件
├── database.js            # 主数据库操作
├── snapshotDatabase.js    # 快照数据库操作
├── autoExport.js          # 自动导出功能
├── snapshotAutoExport.js  # 快照自动导出功能
├── faviconCache.js        # 图标缓存系统
├── public/                # 前端文件
│   ├── index.html         # 主页面
│   ├── snapshot.html      # 快照页面
│   ├── script.js          # 主页面脚本
│   └── snapshot-script.js # 快照页面脚本
├── exports/               # 导出文件目录
├── bookmarks.db           # 主收藏数据库
├── snapshots.db           # 快照收藏数据库
├── start_server.bat       # 启动脚本
├── stop_server.bat        # 停止脚本
└── 使用说明.txt           # 详细使用说明
```

## 🎯 使用说明

### 添加收藏
1. 点击"添加收藏"按钮
2. 填写标题、网址、描述等信息
3. 选择分类和状态
4. 点击保存

### 批量添加
1. 点击"批量添加收藏"按钮
2. 按格式输入：`标题|网址|描述`（每行一条）
3. 设置统一的分类和状态
4. 点击批量添加

### 筛选查看
- 使用搜索框搜索标题
- 选择分类筛选
- 选择状态筛选
- 支持分页浏览

### 导出备份
- 系统自动每3分钟导出一次
- 可手动点击"导出Excel"
- 支持筛选导出

## 🔧 技术栈

- **后端**: Node.js + Express
- **数据库**: SQLite
- **前端**: HTML5 + CSS3 + JavaScript
- **样式**: Tailwind CSS
- **图标**: Material Design Icons
- **导出**: SheetJS (xlsx)

## 📝 更新日志

- ✅ 基础收藏管理功能
- ✅ 书签快照列表功能
- ✅ 批量添加功能
- ✅ 自动导出备份
- ✅ 网站图标显示
- ✅ 响应式界面
- ✅ 主题切换
- ✅ 一键启动脚本
- ✅ 卡片式快照预览

## 📞 技术支持

如遇问题请检查：
- Node.js是否正确安装
- 是否在项目根目录运行脚本
- 端口3004是否被占用
- 查看使用说明.txt获取详细帮助

---

**享受使用网址收藏系统！** 🎉
